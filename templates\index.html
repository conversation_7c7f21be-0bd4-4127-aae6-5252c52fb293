<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可研报告评审助手</title>
    <!-- 替换为本地CSS路径 -->
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <script src="/static/marked.min.js"></script>
    <style>
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .result-table {
            margin-top: 20px;
        }
        .criterion-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.9em;
            padding: 5px 10px;
        }
        .result-符合 { background-color: #d4edda; color: #155724; }
        .result-基本符合 { background-color: #fff3cd; color: #856404; }
        .result-不符合 { background-color: #f8d7da; color: #721c24; }
        .result-不适用 { background-color: #e2e3e5; color: #383d41; }
        .section-detail {
            font-size: 0.9em;
            margin-top: 10px;
        }
        .statistics-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .debug-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-log {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            margin: 0;
        }
        .progress-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .progress-item:last-child {
            border-bottom: none;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">汇总报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reviews">评审管理</a>
                    </li>
                </ul>
                <!-- 全局专题过滤器 -->
                <div class="d-flex align-items-center">
                    <label class="text-white me-2" style="font-size: 0.9em;">专题:</label>
                    <select class="form-select form-select-sm" id="globalTopicFilter" onchange="onGlobalTopicChange()" style="width: 200px;">
                        <option value="">所有专题</option>
                    </select>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <h1 class="text-center mb-4">汇总报告</h1>

        <!-- 汇总报告表格区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">评审结果汇总表</h5>
                    <div class="d-flex align-items-center">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshSummaryTable()" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Tab导航 -->
                <ul class="nav nav-tabs" id="summaryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="criteria-tab" data-bs-toggle="tab" data-bs-target="#criteria-content" type="button" role="tab">
                            审查细则汇总
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary-content" type="button" role="tab">
                            总体分析报告
                        </button>
                    </li>
                </ul>

                <!-- Tab内容 -->
                <div class="tab-content mt-3" id="summaryTabContent">
                    <!-- 审查细则汇总Tab -->
                    <div class="tab-pane fade show active" id="criteria-content" role="tabpanel">
                        <div id="summaryTableContent">
                            <div class="text-center text-muted">
                                <p>请选择专题查看汇总报告</p>
                            </div>
                        </div>
                    </div>

                    <!-- 专题总结报告Tab -->
                    <div class="tab-pane fade" id="summary-content" role="tabpanel">
                        <div id="topicSummaryContent">
                            <div class="text-center text-muted">
                                <p>请选择专题查看总结报告</p>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <button type="button" class="btn btn-success btn-sm" onclick="regenerateTopicSummary()" id="regenerateBtn" style="display: none;">
                                <i class="fas fa-redo"></i> 重新生成总结报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总体分析区域 -->
        <div class="card mb-4" id="overallAnalysisCard" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">总体分析报告</h5>
            </div>
            <div class="card-body">
                <div id="overallAnalysisContent">
                    <!-- 总体分析内容将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 加载动画 -->
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载汇总数据，请稍候...</p>
        </div>

    </div>

    <script>
        // 全局变量
        let allTopics = [];
        let allReports = [];
        let allReviews = [];
        let currentTopicId = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTopics();
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    allTopics = result.data;
                    populateGlobalTopicFilter();

                    // 如果有专题，默认选择第一个
                    if (allTopics.length > 0) {
                        document.getElementById('globalTopicFilter').value = allTopics[0].id;
                        onGlobalTopicChange();
                    }
                }
            } catch (error) {
                console.error('加载专题列表失败:', error);
            }
        }

        // 填充全局专题过滤器
        function populateGlobalTopicFilter() {
            const select = document.getElementById('globalTopicFilter');
            select.innerHTML = '<option value="">所有专题</option>';

            allTopics.forEach(topic => {
                const option = document.createElement('option');
                option.value = topic.id;
                option.textContent = topic.name;
                select.appendChild(option);
            });
        }

        // 全局专题变化处理
        function onGlobalTopicChange() {
            currentTopicId = document.getElementById('globalTopicFilter').value;
            if (currentTopicId) {
                loadSummaryData();
            } else {
                showEmptyMessage();
            }
        }

        // 加载汇总数据
        async function loadSummaryData() {
            if (!currentTopicId) {
                showEmptyMessage();
                return;
            }

            document.querySelector('.loading').style.display = 'block';

            try {
                // 并行加载报告和评审数据
                const [reportsResponse, reviewsResponse] = await Promise.all([
                    fetch(`/api/reports?topic_id=${currentTopicId}`),
                    fetch(`/api/reviews?topic_id=${currentTopicId}`)
                ]);

                const reportsResult = await reportsResponse.json();
                const reviewsResult = await reviewsResponse.json();

                if (reportsResult.success && reviewsResult.success) {
                    allReports = reportsResult.data;
                    allReviews = reviewsResult.data;

                    // 生成汇总表格
                    generateSummaryTable();

                    // 加载总体分析
                    loadOverallAnalysis();
                } else {
                    throw new Error('加载数据失败');
                }
            } catch (error) {
                console.error('加载汇总数据失败:', error);
                showErrorMessage('加载汇总数据失败: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        }

        // 显示空消息
        function showEmptyMessage() {
            document.getElementById('summaryTableContent').innerHTML = `
                <div class="text-center text-muted">
                    <p>请选择专题查看汇总报告</p>
                </div>
            `;
            document.getElementById('overallAnalysisCard').style.display = 'none';
        }

        // 显示错误消息
        function showErrorMessage(message) {
            document.getElementById('summaryTableContent').innerHTML = `
                <div class="alert alert-danger">
                    <p>${message}</p>
                </div>
            `;
            document.getElementById('overallAnalysisCard').style.display = 'none';
        }

        // 生成汇总表格
        function generateSummaryTable() {
            if (!allReviews || allReviews.length === 0) {
                document.getElementById('summaryTableContent').innerHTML = `
                    <div class="alert alert-info">
                        <p class="mb-0">该专题暂无已评审的报告，无法生成汇总表格。</p>
                    </div>
                `;
                return;
            }

            // 获取所有审查细则和详细信息
            const allCriteria = new Map();
            const criteriaDetails = new Map(); // 存储详细分析信息

            allReviews.forEach(review => {
                if (review.result && review.result.criteria_analysis) {
                    review.result.criteria_analysis.forEach(criterion => {
                        if (!allCriteria.has(criterion.criterion_id)) {
                            allCriteria.set(criterion.criterion_id, {
                                id: criterion.criterion_id,
                                content: criterion.criterion_content,
                                results: new Map()
                            });
                            criteriaDetails.set(criterion.criterion_id, new Map());
                        }

                        // 获取报告简称
                        const report = allReports.find(r => r.id === review.report_id);
                        const reportShortName = extractReportShortName(report ? report.name : '未知报告');

                        // 获取评审结果
                        const result = criterion.overall_assessment || criterion.overall_result || '未知';

                        allCriteria.get(criterion.criterion_id).results.set(reportShortName, result);

                        // 存储详细信息用于"不符合"情况的展示
                        criteriaDetails.get(criterion.criterion_id).set(reportShortName, {
                            result: result,
                            comprehensive_analysis: criterion.comprehensive_analysis || '',
                            key_findings: criterion.key_findings || [],
                            recommendations: criterion.recommendations || []
                        });
                    });
                }
            });

            // 生成表格HTML
            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;">编号</th>
                                <th style="width: 40%;">评审细则</th>
                                ${Array.from(new Set(allReviews.map(review => {
                                    const report = allReports.find(r => r.id === review.report_id);
                                    return extractReportShortName(report ? report.name : '未知报告');
                                }))).map(shortName => `<th style="width: 120px;">${shortName}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
            `;

            // 按编号排序审查细则
            const sortedCriteria = Array.from(allCriteria.values()).sort((a, b) => {
                const aNum = parseFloat(a.id);
                const bNum = parseFloat(b.id);
                return aNum - bNum;
            });

            sortedCriteria.forEach(criterion => {
                tableHtml += `
                    <tr>
                        <td><strong>${criterion.id}</strong></td>
                        <td>${criterion.content}</td>
                `;

                // 为每个报告添加结果列
                Array.from(new Set(allReviews.map(review => {
                    const report = allReports.find(r => r.id === review.report_id);
                    return extractReportShortName(report ? report.name : '未知报告');
                }))).forEach(shortName => {
                    const result = criterion.results.get(shortName) || '未评审';
                    const badgeClass = getResultBadgeClass(result);
                    const details = criteriaDetails.get(criterion.id)?.get(shortName);

                    let cellContent = `<span class="badge ${badgeClass}">${result}</span>`;

                    // 如果是"不符合"，添加详细信息的弹出框
                    if (result === '不符合' && details && details.comprehensive_analysis) {
                        const detailId = `detail-${criterion.id}-${shortName.replace(/[^a-zA-Z0-9]/g, '')}`;
                        cellContent += `
                            <br><small>
                                <a href="javascript:void(0)" class="text-decoration-none" onclick="toggleDetail('${detailId}')">
                                    <i class="fas fa-info-circle"></i> 查看详情
                                </a>
                            </small>
                            <div class="collapse mt-2" id="${detailId}">
                                <div class="card card-body p-2 small">
                                    <strong>分析：</strong>${details.comprehensive_analysis}
                                    ${details.recommendations.length > 0 ?
                                        `<br><strong>建议：</strong>${details.recommendations.join('; ')}` : ''}
                                </div>
                            </div>
                        `;
                    }

                    tableHtml += `<td>${cellContent}</td>`;
                });

                tableHtml += `</tr>`;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('summaryTableContent').innerHTML = tableHtml;
        }

        // 提取报告简称
        function extractReportShortName(reportName) {
            // 简化版的报告简称提取逻辑
            if (!reportName) return '未知报告';

            // 提取括号内的县市名称
            let match = reportName.match(/（([^）]*[县市区])）/);
            if (match) return match[1];

            match = reportName.match(/\(([^)]*[县市区])\)/);
            if (match) return match[1];

            // 提取括号内的项目名称
            match = reportName.match(/（([^）]*(?:工程|项目|厂|站|中心|基地|园区|发电厂|变电站))）/);
            if (match) return match[1];

            match = reportName.match(/\(([^)]*(?:工程|项目|厂|站|中心|基地|园区|发电厂|变电站))\)/);
            if (match) return match[1];

            // 备用方案：取前20个字符
            let cleaned = reportName.replace(/^\d+\.?\s*/, '').replace(/广西电网有限责任公司/, '');
            return cleaned.length > 20 ? cleaned.substring(0, 20) + '...' : cleaned;
        }

        // 获取结果徽章样式
        function getResultBadgeClass(result) {
            switch(result) {
                case '符合': return 'bg-success';
                case '基本符合': return 'bg-warning';
                case '不符合': return 'bg-danger';
                case '不适用': return 'bg-secondary';
                default: return 'bg-light text-dark';
            }
        }

        // 加载总体分析
        async function loadOverallAnalysis() {
            if (!currentTopicId) {
                return;
            }

            try {
                const response = await fetch(`/api/topics/${currentTopicId}/summary`);
                const result = await response.json();

                if (result.success && result.data && result.data.summary_result) {
                    displayOverallAnalysis(result.data.summary_result);
                    loadTopicSummaryReport(result.data.summary_result);
                    document.getElementById('overallAnalysisCard').style.display = 'block';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                } else {
                    // 没有总体分析，隐藏卡片
                    document.getElementById('overallAnalysisCard').style.display = 'none';
                    document.getElementById('topicSummaryContent').innerHTML = `
                        <div class="alert alert-info">
                            <p class="mb-0">该专题暂无总结报告，请先完成报告评审。</p>
                        </div>
                    `;
                    document.getElementById('regenerateBtn').style.display = 'none';
                }
            } catch (error) {
                console.error('加载总体分析失败:', error);
                document.getElementById('overallAnalysisCard').style.display = 'none';
                document.getElementById('topicSummaryContent').innerHTML = `
                    <div class="alert alert-danger">
                        <p class="mb-0">加载总结报告失败: ${error.message}</p>
                    </div>
                `;
                document.getElementById('regenerateBtn').style.display = 'none';
            }
        }

        // 加载专题总结报告
        function loadTopicSummaryReport(summaryResult) {
            if (!summaryResult || !summaryResult.summary) {
                document.getElementById('topicSummaryContent').innerHTML = `
                    <div class="alert alert-warning">
                        <p class="mb-0">暂无总结报告内容</p>
                    </div>
                `;
                return;
            }

            // 显示总结报告主体内容
            let content = ``;

            // 如果有详细的comprehensive_data，显示各报告的详细信息
            // if (summaryResult.comprehensive_data && summaryResult.comprehensive_data.length > 0) {
            //     content += `
            //         <div class="card mt-3">
            //             <div class="card-header">
            //                 <h6 class="mb-0"><i class="fas fa-list"></i> 各报告详细评审情况</h6>
            //             </div>
            //             <div class="card-body">
            //                 <div class="row">
            //     `;

            //     summaryResult.comprehensive_data.forEach(reportData => {
            //         content += `
            //             <div class="col-md-6 mb-3">
            //                 <div class="card border-secondary">
            //                     <div class="card-header bg-light">
            //                         <strong>${reportData.report_short_name}</strong>
            //                         <span class="badge bg-primary float-end">${reportData.compliance_rate}%</span>
            //                     </div>
            //                     <div class="card-body p-3">
            //                         ${reportData.overall_assessment ?
            //                             `<p><strong>总体评估：</strong>${reportData.overall_assessment}</p>` : ''}
            //                         ${reportData.comprehensive_analysis ?
            //                             `<p><strong>综合分析：</strong>${reportData.comprehensive_analysis}</p>` : ''}
            //                         ${reportData.key_findings && reportData.key_findings.length > 0 ?
            //                             `<p><strong>关键发现：</strong>${reportData.key_findings.join('; ')}</p>` : ''}
            //                         ${reportData.recommendations && reportData.recommendations.length > 0 ?
            //                             `<p><strong>改进建议：</strong>${reportData.recommendations.join('; ')}</p>` : ''}
            //                     </div>
            //                 </div>
            //             </div>
            //         `;
            //     });

            //     content += `
            //                 </div>
            //             </div>
            //         </div>
            //     `;
            // }

            document.getElementById('topicSummaryContent').innerHTML = content;
        }

        // 重新生成总结报告
        async function regenerateTopicSummary() {
            if (!currentTopicId) {
                alert('请先选择专题');
                return;
            }

            if (!confirm('确定要重新生成总结报告吗？这将覆盖现有的总结报告。')) {
                return;
            }

            const regenerateBtn = document.getElementById('regenerateBtn');
            const originalText = regenerateBtn.innerHTML;
            regenerateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
            regenerateBtn.disabled = true;

            try {
                const response = await fetch(`/api/topics/${currentTopicId}/summary`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        force_regenerate: true
                    })
                });

                const result = await response.json();

                if (result.success && result.data && result.data.summary_result) {
                    loadTopicSummaryReport(result.data.summary_result);
                    displayOverallAnalysis(result.data.summary_result);
                    alert('总结报告重新生成成功！');
                } else {
                    alert('重新生成失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('重新生成总结报告失败:', error);
                alert('重新生成失败: ' + error.message);
            } finally {
                regenerateBtn.innerHTML = originalText;
                regenerateBtn.disabled = false;
            }
        }

        // 显示总体分析
        function displayOverallAnalysis(summaryResult) {
            const container = document.getElementById('overallAnalysisContent');
            const stats = summaryResult.overall_statistics || {};

            let content = `
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-success">${stats.average_compliance_rate || 0}%</h5>
                            <small>平均合规率</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-primary">${stats.reviewed_reports || 0}</h5>
                            <small>已分析报告</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-info">${stats.total_criteria || 0}</h5>
                            <small>总审查细则</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h5 class="text-warning">${stats.common_issues_count || 0}</h5>
                            <small>共性问题</small>
                        </div>
                    </div>
                </div>
            `;

            if (summaryResult.summary) {
                let summaryHtml = '';
                if (typeof summaryResult.summary === 'string') {
                    // summaryHtml = markdownToHtml(summaryResult.summary);
                    summaryHtml = marked.parse(summaryResult.summary);
                } else if (summaryResult.summary && typeof summaryResult.summary === 'object') {
                    summaryHtml = markdownToHtml(JSON.stringify(summaryResult.summary));
                } else {
                    summaryHtml = '<p>暂无内容</p>';
                }

                content += `
                    <div class="p-3 bg-light rounded">
                        ${summaryHtml}
                    </div>
                `;
            }

            container.innerHTML = content;
        }

        // 切换详情显示
        function toggleDetail(detailId) {
            const element = document.getElementById(detailId);
            if (element) {
                if (element.classList.contains('show')) {
                    element.classList.remove('show');
                } else {
                    element.classList.add('show');
                }
            }
        }

        // 将markdown文本转换为HTML
        function markdownToHtml(markdown) {
            if (!markdown) return '';

            // 按行处理
            const lines = markdown.split('\n');
            const result = [];
            let inList = false;
            let listItems = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                // 处理标题
                if (line.startsWith('### ')) {
                    if (inList) {
                        result.push('<ul>' + listItems.join('') + '</ul>');
                        listItems = [];
                        inList = false;
                    }
                    result.push('<h5>' + line.substring(4) + '</h5>');
                } else if (line.startsWith('## ')) {
                    if (inList) {
                        result.push('<ul>' + listItems.join('') + '</ul>');
                        listItems = [];
                        inList = false;
                    }
                    result.push('<h4>' + line.substring(3) + '</h4>');
                } else if (line.startsWith('# ')) {
                    if (inList) {
                        result.push('<ul>' + listItems.join('') + '</ul>');
                        listItems = [];
                        inList = false;
                    }
                    result.push('<h3>' + line.substring(2) + '</h3>');
                }
                // 处理列表项
                else if (line.startsWith('- ') || line.startsWith('* ') || /^\d+\.\s/.test(line)) {
                    const listContent = line.replace(/^[-*]\s/, '').replace(/^\d+\.\s/, '');
                    listItems.push('<li>' + listContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') + '</li>');
                    inList = true;
                }
                // 处理空行
                else if (line === '') {
                    if (inList) {
                        result.push('<ul>' + listItems.join('') + '</ul>');
                        listItems = [];
                        inList = false;
                    }
                    // 空行作为段落分隔
                    if (result.length > 0 && !result[result.length - 1].endsWith('</h3>') &&
                        !result[result.length - 1].endsWith('</h4>') &&
                        !result[result.length - 1].endsWith('</h5>') &&
                        !result[result.length - 1].endsWith('</ul>')) {
                        result.push('</p><p>');
                    }
                }
                // 处理普通段落
                else {
                    if (inList) {
                        result.push('<ul>' + listItems.join('') + '</ul>');
                        listItems = [];
                        inList = false;
                    }
                    // 处理粗体
                    const processedLine = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    result.push(processedLine);
                }
            }

            // 处理最后的列表
            if (inList) {
                result.push('<ul>' + listItems.join('') + '</ul>');
            }

            // 合并结果并包装段落
            let html = result.join('\n');

            // 包装段落（不包装标题和列表）
            const parts = html.split(/(<h[3-5]>.*?<\/h[3-5]>|<ul>.*?<\/ul>)/);
            const wrappedParts = parts.map(part => {
                if (part.startsWith('<h') || part.startsWith('<ul') || part.trim() === '') {
                    return part;
                } else {
                    return '<p>' + part.replace(/\n/g, '<br>') + '</p>';
                }
            });

            return wrappedParts.join('').replace(/<p><\/p>/g, '').replace(/\n/g, '');
        }

        // 刷新汇总表格
        function refreshSummaryTable() {
            if (currentTopicId) {
                loadSummaryData();
            } else {
                alert('请先选择专题');
            }
        }




    </script>

    <!-- Bootstrap JavaScript -->
    <script src="/static/bootstrap.bundle.min.js"></script>
</body>
</html>
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

class ReviewService:
    """评审记录管理服务"""
    
    def __init__(self):
        self.data_dir = "data"
        self.reviews_file = os.path.join(self.data_dir, "reviews.json")
        self._ensure_data_dir()
        self._init_reviews_file()
    
    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def _init_reviews_file(self):
        """初始化评审文件"""
        if not os.path.exists(self.reviews_file):
            self._save_reviews([])
    
    def _load_reviews(self) -> List[Dict[str, Any]]:
        """加载评审数据"""
        try:
            with open(self.reviews_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_reviews(self, reviews: List[Dict[str, Any]]):
        """保存评审数据"""
        with open(self.reviews_file, 'w', encoding='utf-8') as f:
            json.dump(reviews, f, ensure_ascii=False, indent=2)
    
    def create_review(self, report_id: str, topic_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """创建新评审记录"""
        reviews = self._load_reviews()
        
        review = {
            "id": str(uuid.uuid4()),
            "report_id": report_id,
            "topic_id": topic_id,
            "result": result,
            "created_at": datetime.now().isoformat(),
            "status": "completed"
        }
        
        reviews.append(review)
        self._save_reviews(reviews)
        return review
    
    def get_reviews(self, report_id: str = None, topic_id: str = None) -> List[Dict[str, Any]]:
        """获取评审记录列表，可按报告或专题过滤"""
        reviews = self._load_reviews()
        
        if report_id:
            reviews = [review for review in reviews if review.get('report_id') == report_id]
        
        if topic_id:
            reviews = [review for review in reviews if review.get('topic_id') == topic_id]
        
        return reviews

    def get_latest_review_for_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """获取指定报告的最新评审记录"""
        reviews = self.get_reviews(report_id=report_id)
        if not reviews:
            return None

        # 按创建时间排序，返回最新的
        reviews.sort(key=lambda x: x['created_at'], reverse=True)
        return reviews[0]

    def get_review(self, review_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取评审记录"""
        reviews = self._load_reviews()
        return next((review for review in reviews if review['id'] == review_id), None)
    
    def delete_review(self, review_id: str) -> bool:
        """删除评审记录"""
        reviews = self._load_reviews()
        original_length = len(reviews)
        
        reviews = [review for review in reviews if review['id'] != review_id]
        
        if len(reviews) < original_length:
            self._save_reviews(reviews)
            return True
        
        return False
    
    def get_latest_review_for_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """获取报告的最新评审记录"""
        reviews = self.get_reviews(report_id=report_id)
        if not reviews:
            return None
        
        # 按创建时间排序，返回最新的
        reviews.sort(key=lambda x: x['created_at'], reverse=True)
        return reviews[0]
    
    def update_review_status(self, review_id: str, status: str) -> Optional[Dict[str, Any]]:
        """更新评审状态"""
        reviews = self._load_reviews()
        
        for i, review in enumerate(reviews):
            if review['id'] == review_id:
                review['status'] = status
                review['updated_at'] = datetime.now().isoformat()
                
                reviews[i] = review
                self._save_reviews(reviews)
                return review
        
        return None

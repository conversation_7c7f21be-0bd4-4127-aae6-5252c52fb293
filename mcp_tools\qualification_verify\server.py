#!/usr/bin/env python3
"""
资质证明查询验证MCP工具服务器
提供企业资质证明查询和验证功能
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List
from datetime import datetime, timedelta
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.prompts.base import Message

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("qualification_verify")
# 创建MCP服务器实例
server = FastMCP("qualification_verify")

# 模拟企业资质数据库
QUALIFICATION_DB = {
    "广西电网有限责任公司": {
        "company_code": "91450000198765432X",
        "qualifications": [
            {
                "type": "电力工程施工总承包",
                "level": "特级",
                "certificate_no": "D145000001",
                "issue_date": "2020-01-15",
                "expire_date": "2025-01-14",
                "issuing_authority": "住房和城乡建设部",
                "status": "有效"
            },
            {
                "type": "电力工程设计甲级",
                "level": "甲级",
                "certificate_no": "A145000001", 
                "issue_date": "2019-06-20",
                "expire_date": "2024-06-19",
                "issuing_authority": "住房和城乡建设部",
                "status": "有效"
            }
        ],
        "business_scope": "电力工程建设、电网运营、电力设备制造",
        "registration_capital": "1000000万元",
        "legal_representative": "张某某"
    },
    "中国电建集团": {
        "company_code": "91110000123456789A",
        "qualifications": [
            {
                "type": "电力工程施工总承包",
                "level": "特级",
                "certificate_no": "D110000001",
                "issue_date": "2021-03-10",
                "expire_date": "2026-03-09",
                "issuing_authority": "住房和城乡建设部",
                "status": "有效"
            },
            {
                "type": "水利水电工程施工总承包",
                "level": "特级",
                "certificate_no": "D110000002",
                "issue_date": "2020-08-15",
                "expire_date": "2025-08-14",
                "issuing_authority": "住房和城乡建设部",
                "status": "有效"
            }
        ],
        "business_scope": "电力工程、水利工程、基础设施建设",
        "registration_capital": "5000000万元",
        "legal_representative": "李某某"
    }
}


@server.tool()
async def verify_company_qualification(company_name: str, qualification_type: str, certificate_no: str = "") -> List[TextContent]:
    """验证企业资质
    参数:
        company_name: 企业名称
        qualification_type: 资质类型，如：电力工程施工总承包
        certificate_no: 证书编号（可选）
    返回:
        JSON格式的查询结果
    """
    """验证企业资质"""
    company_name = arguments.get("company_name", "").strip()
    qualification_type = arguments.get("qualification_type", "").strip()
    certificate_no = arguments.get("certificate_no", "").strip()
    
    if not company_name or not qualification_type:
        return [TextContent(
            type="text",
            text="错误：企业名称和资质类型不能为空"
        )]
    
    # 查找企业
    company_data = None
    for company, data in QUALIFICATION_DB.items():
        if company_name in company or company in company_name:
            company_data = data
            break
    
    if not company_data:
        return [TextContent(
            type="text",
            text=f"未找到企业 '{company_name}' 的资质信息"
        )]
    
    # 查找匹配的资质
    matching_qualifications = []
    for qual in company_data["qualifications"]:
        if qualification_type in qual["type"] or qual["type"] in qualification_type:
            if not certificate_no or qual["certificate_no"] == certificate_no:
                matching_qualifications.append(qual)
    
    if not matching_qualifications:
        return [TextContent(
            type="text",
            text=f"企业 '{company_name}' 未找到匹配的资质 '{qualification_type}'"
        )]
    
    # 检查资质有效性
    current_date = datetime.now()
    for qual in matching_qualifications:
        expire_date = datetime.strptime(qual["expire_date"], "%Y-%m-%d")
        qual["is_valid"] = expire_date > current_date
        qual["days_to_expire"] = (expire_date - current_date).days
    
    result = {
        "company_name": company_name,
        "qualification_type": qualification_type,
        "verification_result": "通过" if matching_qualifications else "未通过",
        "matching_qualifications": matching_qualifications,
        "verification_time": current_date.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

@server.tool()
async def query_company_info(company_name: str) -> List[TextContent]:
    """查询企业信息
    参数:
        company_name: 企业名称
    返回:
        JSON格式的企业信息
    """
    company_name = company_name.strip()
    
    if not company_name:
        return [TextContent(
            type="text",
            text="错误：企业名称不能为空"
        )]
    
    # 查找企业
    company_data = None
    matched_name = None
    for company, data in QUALIFICATION_DB.items():
        if company_name in company or company in company_name:
            company_data = data
            matched_name = company
            break
    
    if not company_data:
        return [TextContent(
            type="text",
            text=f"未找到企业 '{company_name}' 的信息"
        )]
    
    # 检查所有资质的有效性
    current_date = datetime.now()
    for qual in company_data["qualifications"]:
        expire_date = datetime.strptime(qual["expire_date"], "%Y-%m-%d")
        qual["is_valid"] = expire_date > current_date
        qual["days_to_expire"] = (expire_date - current_date).days
    
    result = {
        "company_name": matched_name,
        "company_code": company_data["company_code"],
        "legal_representative": company_data["legal_representative"],
        "registration_capital": company_data["registration_capital"],
        "business_scope": company_data["business_scope"],
        "total_qualifications": len(company_data["qualifications"]),
        "qualifications": company_data["qualifications"],
        "query_time": current_date.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

@server.tool()
async def check_qualification_validity(certificate_no: str) -> List[TextContent]:
    """检查资质证书的有效性（是否过期）
    参数:
        certificate_no: 证书编号
    返回:
        JSON格式的证书有效性信息
    """
    certificate_no = certificate_no.strip()
    
    if not certificate_no:
        return [TextContent(
            type="text",
            text="错误：证书编号不能为空"
        )]
    
    # 在所有企业中查找证书
    found_qualification = None
    found_company = None
    
    for company, data in QUALIFICATION_DB.items():
        for qual in data["qualifications"]:
            if qual["certificate_no"] == certificate_no:
                found_qualification = qual.copy()
                found_company = company
                break
        if found_qualification:
            break
    
    if not found_qualification:
        return [TextContent(
            type="text",
            text=f"未找到证书编号 '{certificate_no}' 的资质信息"
        )]
    
    # 检查有效性
    current_date = datetime.now()
    expire_date = datetime.strptime(found_qualification["expire_date"], "%Y-%m-%d")
    is_valid = expire_date > current_date
    days_to_expire = (expire_date - current_date).days
    
    result = {
        "certificate_no": certificate_no,
        "company_name": found_company,
        "qualification_type": found_qualification["type"],
        "level": found_qualification["level"],
        "issue_date": found_qualification["issue_date"],
        "expire_date": found_qualification["expire_date"],
        "issuing_authority": found_qualification["issuing_authority"],
        "is_valid": is_valid,
        "days_to_expire": days_to_expire,
        "status": "有效" if is_valid else "已过期",
        "check_time": current_date.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

def main():
    """启动MCP服务器"""
    logger.info("启动资质证明查询验证MCP服务器...")
    server.run(transport='stdio')

if __name__ == "__main__":
    main()

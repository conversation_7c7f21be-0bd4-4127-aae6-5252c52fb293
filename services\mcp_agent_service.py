import os
import json
import asyncio
import logging
from typing import Dict, Any, List, Optional
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 用于区分工具类别与工具接口名称的分隔符(其他分隔符如.，在mindie侧会报错
TOOL_SPLITTER = "_"

class MCPAgentService:
    """MCP智能体服务，管理多个MCP工具的连接和调用"""

    def __init__(self):
        self.sessions: Dict[str, Any] = {}
        self.exit_stack = None
        self.available_tools: Dict[str, List[Dict]] = {}
        self.logger = logging.getLogger(__name__)

        # 配置MCP工具路径
        self.mcp_tools_config = {
            "poverty": {
                "path": "mcp_tools/poverty_county_query/server.py",
                "description": "贫困县查询工具，可以查询指定地区是否为贫困县"
            },
            "qualification": {
                "path": "mcp_tools/qualification_verify/server.py",
                "description": "资质证明查询验证工具，可以验证企业资质证明的真实性"
            },
            "energy": {
                "path": "mcp_tools/energy_price_query/server.py",
                "description": "能源局估算单价表查询工具，可以查询各类能源设备的估算单价"
            }
        }

    async def initialize(self):
        """初始化所有MCP工具连接"""

        self.logger.info("开始初始化MCP工具连接...")
        self.exit_stack = AsyncExitStack()
        try:
            for tool_name, config in self.mcp_tools_config.items():
                try:
                    await self._connect_tool(tool_name, config["path"])
                    self.logger.info(f"成功连接MCP工具: {tool_name}")
                except Exception as e:
                    self.logger.warning(f"连接MCP工具失败 {tool_name}: {e}")
                    # 继续连接其他工具，不因单个工具失败而中断
                    continue
        except Exception as e:
            await self.exit_stack.aclose()
            raise
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
    async def _connect_tool(self, tool_name: str, server_path: str):
        """连接单个MCP工具"""
        
        # 检查服务器文件是否存在
        if not os.path.exists(server_path):
            raise FileNotFoundError(f"MCP工具服务器文件不存在: {server_path}")
        
        # 配置服务器参数
        server_params = StdioServerParameters(
            command="python3",
            args=[server_path],
            env={
                "PATH": os.path.join(os.path.dirname(server_path), ".venv", "bin") + ":" + os.environ.get("PATH", ""),
                "VIRTUAL_ENV": os.path.join(os.path.dirname(server_path), ".venv"),
            }
        )
        
        # 建立连接
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        stdio, write = stdio_transport
        session = await self.exit_stack.enter_async_context(ClientSession(stdio, write))
        
        # 初始化会话
        await session.initialize()
        
        # 获取工具列表
        response = await session.list_tools()
        tools = response.tools
        
        # 存储会话和工具信息
        self.sessions[tool_name] = session
        self.available_tools[tool_name] = [
            {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema
            }
            for tool in tools
        ]
        
        self.logger.info(f"工具 {tool_name} 提供的功能: {[tool.name for tool in tools]}")
    
    def get_all_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用的MCP工具列表，格式化为OpenAI工具格式"""
        all_tools = []
        
        for tool_category, tools in self.available_tools.items():
            for tool in tools:
                all_tools.append({
                    "type": "function",
                    "function": {
                        "name": f"{tool_category}{TOOL_SPLITTER}{tool['name']}",
                        "description": f"[{tool_category}] {tool['description']}",
                        "parameters": tool["parameters"]
                    }
                })
        
        return all_tools
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用指定的MCP工具"""
        # 解析工具名称，格式为 "toolcategory_tool_name"
        parts = tool_name.split({TOOL_SPLITTER}, 1)
        if len(parts) < 2:
            raise ValueError(f"无效的工具名称格式: {tool_name}")

        tool_category = parts[0]
        actual_tool_name = parts[1]

        # 检查工具是否存在
        if tool_category not in self.sessions:
            raise ValueError(f"MCP工具类别不存在: {tool_category}")

        self.logger.info(f"调用MCP工具: {tool_category}.{actual_tool_name}, 参数: {arguments}")

        session = self.sessions[tool_category]

        try:
            # 调用工具
            result = await session.call_tool(actual_tool_name, arguments)

            if result.isError:
                self.logger.error(f"MCP工具调用失败: {result.content}")
                return {
                    "success": False,
                    "error": str(result.content),
                    "tool": tool_name
                }

            # 处理结果内容
            content = self._process_tool_result(result.content)

            self.logger.info(f"MCP工具调用成功: {tool_name}")
            return {
                "success": True,
                "result": content,
                "tool": tool_name
            }

        except Exception as e:
            self.logger.error(f"MCP工具调用异常: {tool_name}, 错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name
            }

    def _process_tool_result(self, content) -> str:
        """处理MCP工具返回的结果内容"""
        if isinstance(content, list):
            return "\n".join([self._convert_item(c) for c in content])
        return self._convert_item(content)
    
    def _convert_item(self, item) -> str:
        """转换单个内容项"""
        if hasattr(item, 'text'):
            return item.text
        if hasattr(item, 'data'):
            return json.dumps(item.data, ensure_ascii=False, indent=2)
        return str(item)
    
    def get_tools_summary(self) -> Dict[str, Any]:
        """获取工具摘要信息"""
        summary = {
            "total_categories": len(self.sessions),
            "total_tools": sum(len(tools) for tools in self.available_tools.values()),
            "categories": {}
        }
        
        for category, tools in self.available_tools.items():
            summary["categories"][category] = {
                "tool_count": len(tools),
                "tools": [tool["name"] for tool in tools],
                "connected": category in self.sessions
            }
        
        return summary
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("清理MCP工具连接...")
        if self.exit_stack:
            # 使用当前事件循环确保在正确上下文中执行
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(None, self._safe_aclose)
            #await self.exit_stack.aclose()
        self.sessions.clear()
        self.available_tools.clear()
    
    def _safe_aclose(self):
        """安全关闭AsyncExitStack的同步包装"""
        try:
            # 在同步上下文中执行异步关闭
            asyncio.run(self.exit_stack.aclose())
        except RuntimeError as e:
            if "different task" in str(e):
                # 处理跨任务错误
                self.logger.warning("安全处理跨任务关闭: %s", e)
            else:
                self.logger.error("关闭失败: %s", e)

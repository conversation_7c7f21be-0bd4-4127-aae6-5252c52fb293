你是专业的可研报告评审专家，之前的任务已经使用大模型针对每个审查细则，给出了各章节的审查情况，请根据这些结果，生成一份针对整份报告的总体评审意见。

# 评审结果
{review_results}

# 输出格式

请按以下JSON格式给出总体评审意见：
```json
{{
  "overall_conclusion": "符合/基本符合/不符合",
  "compliance_rate": "合规率百分比",
  "major_issues": [
    "主要问题1",
    "主要问题2"
  ],
  "improvement_suggestions": [
    "改进建议1",
    "改进建议2"
  ],
  "summary_text": "总体评审意见的文字描述"
}}
```

注意：
1. 基于实际统计数据进行总结
2. 重点关注不符合项目
3. 建议要具体可操作
4. 输出必须是有效的JSON格式

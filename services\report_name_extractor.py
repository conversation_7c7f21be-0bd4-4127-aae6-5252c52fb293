"""
报告简称提取服务
用于从报告名称中提取县市名称或项目名称作为简称
"""
import re
from typing import Optional


class ReportNameExtractor:
    """报告简称提取器"""
    
    def __init__(self):
        # 常见的县市名称模式
        self.county_patterns = [
            r'（([^）]*[县市区])）',  # 括号内的县市区名称
            r'\(([^)]*[县市区])\)',   # 英文括号内的县市区名称
            r'([^，。、]*[县市区])',    # 直接的县市区名称
        ]
        
        # 项目名称模式（当不是县市时）
        self.project_patterns = [
            r'（([^）]*(?:工程|项目|厂|站|中心|基地|园区|发电厂|变电站))）',  # 括号内的项目名称
            r'\(([^)]*(?:工程|项目|厂|站|中心|基地|园区|发电厂|变电站))\)',   # 英文括号内的项目名称
        ]
        
        # 需要过滤的词汇
        self.filter_words = [
            '可行性研究报告', '项目', '工程', '建设', '投资', '计划', '需求',
            '广西电网', '有限责任公司', '中央预算内', '农村电网', '巩固提升'
        ]
    
    def extract_short_name(self, report_name: str) -> str:
        """
        从报告名称中提取简称
        
        Args:
            report_name: 完整的报告名称
            
        Returns:
            提取的简称，如果无法提取则返回原名称的前20个字符
        """
        if not report_name:
            return "未知报告"
        
        # 首先尝试提取县市名称
        county_name = self._extract_county_name(report_name)
        if county_name:
            return county_name
        
        # 如果没有县市名称，尝试提取项目名称
        project_name = self._extract_project_name(report_name)
        if project_name:
            return project_name
        
        # 如果都没有，返回清理后的报告名称前部分
        return self._extract_fallback_name(report_name)
    
    def _extract_county_name(self, report_name: str) -> Optional[str]:
        """提取县市名称"""
        for pattern in self.county_patterns:
            match = re.search(pattern, report_name)
            if match:
                county_name = match.group(1).strip()
                # 清理县市名称
                county_name = self._clean_name(county_name)
                if county_name and len(county_name) <= 10:  # 县市名称通常不会太长
                    return county_name
        return None
    
    def _extract_project_name(self, report_name: str) -> Optional[str]:
        """提取项目名称"""
        for pattern in self.project_patterns:
            match = re.search(pattern, report_name)
            if match:
                project_name = match.group(1).strip()
                # 清理项目名称
                project_name = self._clean_name(project_name)
                if project_name and len(project_name) <= 20:  # 项目名称可以稍长
                    return project_name
        return None
    
    def _clean_name(self, name: str) -> str:
        """清理名称，移除不必要的词汇"""
        cleaned_name = name
        
        # 移除过滤词汇
        for word in self.filter_words:
            cleaned_name = cleaned_name.replace(word, '')
        
        # 移除多余的标点符号和空格
        cleaned_name = re.sub(r'[，。、；：！？\s]+', '', cleaned_name)
        
        return cleaned_name.strip()
    
    def _extract_fallback_name(self, report_name: str) -> str:
        """备用方案：提取报告名称的前部分作为简称"""
        # 移除常见的前缀
        cleaned_name = report_name
        
        # 移除数字编号
        cleaned_name = re.sub(r'^\d+\.?\s*', '', cleaned_name)
        
        # 移除公司名称
        for company in ['广西电网有限责任公司', '国家电网', '南方电网']:
            cleaned_name = cleaned_name.replace(company, '')
        
        # 移除年份
        cleaned_name = re.sub(r'\d{4}年', '', cleaned_name)
        
        # 取前20个字符
        if len(cleaned_name) > 20:
            cleaned_name = cleaned_name[:20] + '...'
        
        return cleaned_name.strip() or "未知报告"


# 测试函数
def test_extractor():
    """测试报告简称提取功能"""
    extractor = ReportNameExtractor()
    
    test_cases = [
        "1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告",
        "2.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市第二发电厂）可行性研究报告",
        "3.南宁市青秀区电网改造工程可行性研究报告",
        "4.柳州市工业园区供电工程项目可行性研究报告",
        "5.某某变电站扩建工程可行性研究报告",
        "6.广西电网2024年投资计划项目可行性研究报告"
    ]
    
    print("报告简称提取测试:")
    print("=" * 80)
    
    for i, report_name in enumerate(test_cases, 1):
        short_name = extractor.extract_short_name(report_name)
        print(f"{i}. 原名称: {report_name}")
        print(f"   简称: {short_name}")
        print()


if __name__ == "__main__":
    test_extractor()

import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

class TopicService:
    """报告专题管理服务"""

    def __init__(self):
        self.data_dir = "data"
        self.topics_file = os.path.join(self.data_dir, "topics.json")
        self._ensure_data_dir()
        self._init_topics_file()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def _init_topics_file(self):
        """初始化专题文件"""
        if not os.path.exists(self.topics_file):
            self._save_topics([])

    def _load_topics(self) -> List[Dict[str, Any]]:
        """加载专题数据"""
        try:
            with open(self.topics_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def _save_topics(self, topics: List[Dict[str, Any]]):
        """保存专题数据"""
        with open(self.topics_file, 'w', encoding='utf-8') as f:
            json.dump(topics, f, ensure_ascii=False, indent=2)

    def create_topic(self, name: str, description: str = "",
                    outline_file: str = "", guide_file: str = "",
                    criteria_file: str = "", start_date: str = "",
                    end_date: str = "") -> Dict[str, Any]:
        """创建新专题"""
        topics = self._load_topics()

        # 检查专题名称是否已存在
        if any(topic['name'] == name for topic in topics):
            raise ValueError(f"专题名称 '{name}' 已存在")

        topic = {
            "id": str(uuid.uuid4()),
            "name": name,
            "description": description,
            "outline_file": outline_file,
            "guide_file": guide_file,
            "criteria_file": criteria_file,
            "start_date": start_date,
            "end_date": end_date,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        topics.append(topic)
        self._save_topics(topics)
        return topic

    def get_topics(self) -> List[Dict[str, Any]]:
        """获取所有专题"""
        return self._load_topics()

    def get_topic(self, topic_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取专题"""
        topics = self._load_topics()
        return next((topic for topic in topics if topic['id'] == topic_id), None)

    def update_topic(self, topic_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """更新专题"""
        topics = self._load_topics()

        for i, topic in enumerate(topics):
            if topic['id'] == topic_id:
                # 更新字段
                for key, value in kwargs.items():
                    if key in ['name', 'description', 'outline_file', 'guide_file', 'criteria_file', 'start_date', 'end_date']:
                        topic[key] = value
                topic['updated_at'] = datetime.now().isoformat()

                topics[i] = topic
                self._save_topics(topics)
                return topic

        return None

    def delete_topic(self, topic_id: str) -> bool:
        """删除专题"""
        # 检查是否有报告使用该专题
        from services.report_service import ReportService

        report_service = ReportService()

        # 检查该专题下是否有报告
        reports = report_service.get_reports(topic_id=topic_id)
        if reports:
            raise ValueError(f"无法删除专题，该专题下还有 {len(reports)} 个报告")

        topics = self._load_topics()
        original_length = len(topics)

        # 删除专题相关的汇总报告
        summary_file = os.path.join(self.data_dir, f"topic_summary_{topic_id}.json")
        if os.path.exists(summary_file):
            try:
                os.remove(summary_file)
                print(f"已删除专题汇总报告: {summary_file}")
            except Exception as e:
                print(f"删除专题汇总报告失败: {e}")

        topics = [topic for topic in topics if topic['id'] != topic_id]

        if len(topics) < original_length:
            self._save_topics(topics)
            return True

        return False

    def get_topic_files(self, topic_id: str) -> Dict[str, str]:
        """获取专题的配置文件路径"""
        topic = self.get_topic(topic_id)
        if not topic:
            return {}

        return {
            "outline_file": topic.get("outline_file", ""),
            "guide_file": topic.get("guide_file", ""),
            "criteria_file": topic.get("criteria_file", "")
        }

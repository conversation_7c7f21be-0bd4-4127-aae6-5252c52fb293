![](data:image/png;base64...)

**密码+人工智能一体机管理系统项目需求规格说明书**

**V1.0**

**三未信安科技股份有限公司**

**2025年04月03日**

**版本修订说明**

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| 版本 | 修改内容 | 修改人 | 修改日期 | 审核人 | 发布日期 |
| V1.0 | 创建 | 万军 | 2025.04.03 |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |

目录

[目录 3](#_Toc1914045612)

[1. 简介 7](#_Toc185829783)

[1.1. 术语和缩写 7](#_Toc1173519660)

[1.2. 参考资料 7](#_Toc396258033)

[2. 产品描述 8](#_Toc648375159)

[3. 用户业务场景分析 9](#_Toc2131647078)

[4. 与现有产品差异 9](#_Toc352700055)

[5. 约束与限制 9](#_Toc873618687)

[6. 需求详细描述 9](#_Toc1775917549)

[6.1. 产品运行环境 10](#_Toc591103258)

[6.2. 功能清单 11](#_Toc247953873)

[6.3. 功能说明 11](#_Toc613879997)

[6.4. 监控组件 12](#_Toc445098540)

[6.4.1. 监控组件适配 12](#_Toc2077665815)

[PR-F-1001 监控组件安装适配 12](#_Toc650516015)

[PR-F-1002 监控组件功能测试 13](#_Toc764649192)

[6.4.2. 监控组件功能扩展 13](#_Toc1509625524)

[PR-F-1101 AI一体机特殊指标整理 13](#_Toc658646039)

[PR-F-1102 AI一体机特殊指标获取 14](#_Toc912648605)

[6.5. 管理系统相关需求 14](#_Toc530200998)

[6.5.1. secuLlama服务管理 14](#_Toc774328827)

[6.2.1.1 secuLlama服务启动 14](#_Toc1965667685)

[secuLlama服务启动 14](#_Toc138702069)

[6.2.1.2 secuLlama服务停止 14](#_Toc1797120644)

[secuLlama服务停止 14](#_Toc667005904)

[6.2.1.3 secuLlama服务状态查看 15](#_Toc92362033)

[secuLlama服务状态查看 15](#_Toc248488684)

[6.2.1.4 secuLlama服务版本查看 15](#_Toc253903586)

[secuLlama服务版本查看 15](#_Toc633744433)

[6.5.2. 模型管理 16](#_Toc1987053283)

[6.5.2.1. 查看正在运行的模型 16](#_Toc1656620039)

[查看正在运行的模型 16](#_Toc400306397)

[6.5.3. 监控管理 16](#_Toc25399418)

[6.5.3.1. 监控面板开发 16](#_Toc682656051)

[监控面板开发 16](#_Toc796564430)

[6.5.3.2. AI一体机特殊指标梳理 17](#_Toc673774577)

[AI一体机特殊指标梳理 17](#_Toc666819481)

[~~6.5.3.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发~~ 17](#_Toc1149264486)

[告警管理 17](#_Toc1547393265)

[通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告警联系人、告警列表、邮件服务器配置、告警历史等。 17](#_Toc295253382)

[6.5.4. 安全认证 17](#_Toc1740367744)

[6.5.4.1. 基于Ukey的认证 18](#_Toc1795347138)

[基于Ukey的认证 18](#_Toc909133379)

[6.5.4.2. 基于配置文件的认证 18](#_Toc37982636)

[基于配置文件的认证 18](#_Toc1725529305)

[6.6. 智能应用统一门户相关需求 18](#_Toc1559649394)

[6.6.1. 基础网关相关需求 18](#_Toc802631828)

[6.6.1.1. 用户身份和用户类型 19](#_Toc1087671181)

[用户身份和用户类型 19](#_Toc70811786)

[6.6.1.2. 门户管理服务API代理和鉴权 19](#_Toc1715280433)

[门户管理服务API代理和鉴权 19](#_Toc1617872179)

[6.6.1.3. 门户访问代理和鉴权 20](#_Toc845140613)

[门户访问代理和鉴权 20](#_Toc1533464470)

[6.6.1.4. 应用访问代理和鉴权 20](#_Toc1756574248)

[应用访问代理和鉴权 20](#_Toc494777609)

[6.6.2. 门户管理员管理 20](#_Toc52986726)

[6.6.2.1. 新增门户管理员 21](#_Toc1848936281)

[新增门户管理员 21](#_Toc743266294)

[6.6.2.2. 编辑门户管理员 21](#_Toc306890312)

[编辑门户管理员 21](#_Toc335197066)

[6.6.2.3. 删除门户管理员 21](#_Toc582835929)

[删除门户管理员 21](#_Toc1963510351)

[6.6.2.4. 门户管理员列表 22](#_Toc735503463)

[门户管理员列表 22](#_Toc608235348)

[6.6.2.5. 门户管理员登录 22](#_Toc498682754)

[门户管理员登录 22](#_Toc1532067894)

[6.6.2.6. 门户管理员退出 23](#_Toc1282009925)

[门户管理员退出 23](#_Toc1165502235)

[6.6.3. 门户用户管理 23](#_Toc533848732)

[6.6.3.1. 新增用户 24](#_Toc681919542)

[新增用户 24](#_Toc1460755617)

[6.6.3.2. 编辑用户 24](#_Toc126732828)

[编辑用户 24](#_Toc329783033)

[6.6.3.3. 删除用户 25](#_Toc222405348)

[删除用户 25](#_Toc164715464)

[6.6.3.4. 门户用户列表 25](#_Toc2055312338)

[门户用户列表 25](#_Toc1782054743)

[6.6.3.5. 门户用户登录 26](#_Toc967347292)

[门户用户登录 26](#_Toc995499872)

[6.6.3.6. 门户用户退出 26](#_Toc1852866529)

[门户用户退出 26](#_Toc535144078)

[6.6.3.7. 启用门户用户 27](#_Toc465888403)

[启用门户用户 27](#_Toc550523494)

[6.6.3.8. 禁用门户用户 27](#_Toc2068608548)

[禁用门户用户 27](#_Toc74979004)

[6.6.4. 门户管理（Dify实例管理） 28](#_Toc1045301103)

[6.6.4.1. 创建门户 28](#_Toc2121595275)

[创建门户 28](#_Toc1923915285)

[6.6.4.2. 编辑门户信息 28](#_Toc1788567397)

[编辑门户信息 28](#_Toc281001939)

[6.6.4.3. 门户列表 29](#_Toc111628704)

[门户列表 29](#_Toc223919679)

[6.6.4.4. 启动门户 29](#_Toc97028643)

[门户列表 29](#_Toc847132167)

[6.6.4.5. 停止门户 30](#_Toc832155027)

[停止门户 30](#_Toc595711397)

[6.6.4.6. 删除门户 30](#_Toc231716413)

[删除门户 30](#_Toc2114164952)

[6.6.4.7. 跳转管理端 31](#_Toc1761213633)

[跳转管理端 31](#_Toc765565145)

[6.6.4.8. 跳转门户 31](#_Toc648600847)

[跳转管理端 31](#_Toc1074485602)

[6.6.4.9. 首页配置 32](#_Toc892297973)

[首页配置 32](#_Toc978383880)

[6.6.4.10. 访问控制配置 32](#_Toc1296890951)

[访问控制配置 32](#_Toc1057013437)

[6.6.5. Dify容器化多实例部署改造 33](#_Toc886212570)

[6.6.5.1. Dify多实例部署&网络隔离&目录隔离 33](#_Toc931462046)

[Dify多实例部署&网络隔离&目录隔离 33](#_Toc2024360730)

[6.6.5.2. Dify实例连接外部数据库 33](#_Toc1881712442)

[Dify实例连接外部数据库 33](#_Toc636844927)

[6.6.5.3. Dify实例连接外部Redis 34](#_Toc412021160)

[Dify实例连接外部Redis 34](#_Toc200117198)

[6.6.5.4. 初始化供应商和模型 34](#_Toc1187368421)

[初始化供应商和模型 34](#_Toc333146060)

[6.6.5.5. Dify实例自动化部署 34](#_Toc275096202)

[Dify实例自动化部署 34](#_Toc85185876)

[6.6.6. 应用市场 35](#_Toc307257687)

[6.6.6.1. 应用模板管理 35](#_Toc51527839)

[应用模板管理 35](#_Toc1873753274)

[6.6.6.2. 应用模板列表 35](#_Toc588259627)

[应用模板列表 35](#_Toc163156543)

[6.6.6.3. 应用安装 36](#_Toc2097672953)

[应用安装 36](#_Toc685288270)

[6.6.7. 统一门户 36](#_Toc1010288711)

[6.6.7.1. 访问门户首页 36](#_Toc782344332)

[访问门户首页 36](#_Toc1280999667)

[6.6.7.2. 用户信息展示 37](#_Toc1242005124)

[门户用户信息 37](#_Toc749025636)

[6.6.7.3. 门户首页对话 38](#_Toc894729652)

[门户首页对话 38](#_Toc2007570270)

[6.6.7.4. 应用探索（门户应用列表） 38](#_Toc1397626483)

[门户应用列表 38](#_Toc1969215255)

[6.6.7.5. 应用对话页 39](#_Toc752384595)

[应用对话页 39](#_Toc228526715)

[6.6.7.6. 用户收藏应用、取消收藏应用 40](#_Toc1118622558)

[用户收藏应用、取消收藏 40](#_Toc1809398033)

[6.7. 接口需求 41](#_Toc1114739286)

[6.8. 界面需求 41](#_Toc2050084604)

[6.9. 性能需求 41](#_Toc1686275115)

[6.10. 可靠性/可用性需求 41](#_Toc848968080)

[6.11. 安全性需求 42](#_Toc539445883)

[6.11.1. 数据脱敏 42](#_Toc2098296275)

[6.11.2. 数据围栏组件适配 43](#_Toc1049085278)

[6.12. 可维护性需求 43](#_Toc1726814304)

[6.13. 工作状态需求 43](#_Toc283958687)

[6.14. 结构需求 44](#_Toc1324181480)

[6.15. 环保需求 44](#_Toc1812000180)

[6.16. 认证需求 45](#_Toc591216375)

[6.17. 用户文档需求 45](#_Toc1375709320)

[6.18. 客户特殊需求 45](#_Toc1538269806)

[6.19. 法律法规要求 46](#_Toc1179476002)

[6.20. 国家及行业标准要求 46](#_Toc1538865863)

[6.21. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》) 46](#_Toc1488459111)

[6.22. 其他需求 46](#_Toc1864764272)

# 简介

* 1. 术语和缩写

|  |  |  |
| --- | --- | --- |
| **编号** | **名词** | **说明** |
| 1 | CryptoAI OneSystem | 密码+人工智能一体机管理系统 |
| 2 | secuLlama | 基于Ollama添加安全访问策略之后的大模型运行框架。 |
| 3 | vLLM | Python库，大模型推理引擎。 |
| 3 | secuLlm | 自研大模型运行框架，实现多vLLM进程服务管理及API请求转发，并添加安全访问策略。 |
| 5 | Dify | Dify 是一款开源的大语言模型(LLM) 应用开发平台。它融合了后端即服务（Backend as Service）和 LLMOps 的理念，使开发者可以快速搭建生产级的生成式 AI 应用 |
| 6 | 知识库 | 知识库（Knowledge）是一系列文档（Documents）的集合，一个文档内可能包含多组内容分段（Chunks），知识库可以被整体集成至一个应用中作为检索上下文使用，用户可以将企业内部文档、FAQ、规范信息等内容上传至知识库，知识库会自动进行结构化处理。当LLM 接收到用户的问题后，将首先基于关键词在知识库内检索内容。知识库将根据关键词，召回相关度排名较高的内容区块，向 LLM 提供关键上下文以辅助其生成更加精准的回答 |
| 6 | 应用 | 应用是指基于 GPT 等大语言模型构建的实际场景应用。通过创建应用，用户可以将智能 AI 技术应用于特定的需求，应用类型包括：聊天助手、文档生成应用、Agent、对话流、工作流。 |
| 7 | 聊天助手应用 | 基于 LLM 构建对话式交互的助手，通过交互式界面，采用一问一答模式与用户持续对话，可以用在客户服务、在线教育、医疗保健、金融服务等领域。这些应用可以帮助组织提高工作效率、减少人工成本和提供更好的用户体验。 |
| 8 | 文本生成应用 | 面向文本生成类任务的助手，例如撰写故事、文本分类、翻译等 |
| 9 | Agent应用 | 能够分解任务、推理思考、调用工具的对话式智能助手 |
| 10 | 对话流应用 | 适用于定义等复杂流程的多轮对话场景，具有记忆功能的应用编排方式 |
| 11 | 工作流应用 | 适用于自动化、批处理等单轮生成类任务的场景的应用编排方式 |

* 1. 参考资料

*包括引用的项目内资料、客户方资料等等。*

# 产品描述

三未信安基于密码安全与AI技术的深厚积累，针对大模型本地化部署中的安全挑战，倾力打

造"密码+"人工智能一体机产品。

产品深度融合国产硬件、大模型RAG框架及全栈式安全防护能力，围绕“硬件+软件+服务”

提供身份认证、数据隔离、内容过滤、模型保护等核心功能模块。面向各行业智能化升级需求，形成集智能问答交互、文档分析处理、数据治理优化等多功能于一体的“开箱即用”解决方案。通过对算力资源、算法模型与安全机制的统筹设计，全面兼顾业务效率与合规要求，真正实现模型部署易、管理省、安全强，让企业在智能化转型中稳步前行。

三未信安AI一体机管理系统用于管理、监控AI一体机，利用该系统，可以将后台执行的功能通过页面来提供，方便客户使用、管理、监控一体机。该管理系统的整体架构图如下，核心为应用层：

![](data:image/png;base64...)

说明：管理系统不涉及利用大模型进行推理、会话等业务，仅用来提供运维功能。具体的业务管理、运营功能敬请期待后续产品。

# 用户业务场景分析

1. 运维场景：通过简单的操作即可将大模型轻松运行起来，提供服务，大大降低运维人员的运维压力，降低AI使用的难度；

2. 监控场景：通过管理系统可以实现对一体机的全面、实时监控，可以快速、准确的检测到设备的实时状态，在问题出现时，能够快速定位问题原因；

3. 性能优化场景：由于降低了运维压力，且提供了实时的监控，运维人员可以投入更多的精力到大模型性能提升上，通过直观的监控，可能更加清晰的找到性能瓶颈点；

4. 数据脱敏场景：提供一系列针对敏感数据的识别和处置方案，应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理；

5. 数据围栏场景：提供输入输出内容风险检测的能力，帮助用户发现色情、暴力、惊悚、敏感、禁限、辱骂等风险内容或元素，降低人工审核成本，提升内容质量；

6. 数据隔离场景：通过多Dify实例的管理功能+租户管理，为每个租户构建独立知识库，确保数据互不干扰，实现知识库隔离。同时，依据不同租户需求，通过精细化的AI应用权限管理，保障各租户在使用AI应用时的数据安全与权限边界明确。

# 与现有产品差异

|  |  |  |
| --- | --- | --- |
| 现有产品功能点 | 新需求差异描述 | 备注 |
| 当前AI一体机没有管理页面，只能通过后台命令进行管理；且不具备监控功能。 | 增加管理页面，支持通过页面进行简单的运维操作；支持监控管理，实现对一体机状态的实时监控。 |  |
| 当前AI一体机不具有敏感数据脱敏处理的能力，存在敏感数据泄漏风险。 | 支持多种数据格式，支持多种敏感数据识别规则，支持多种脱敏算法。 |  |
| 当前AI大模型仅支持简单的关键词屏蔽或黑白名单规则，无法处理复杂语义或变体风险内容。 | 针对不良价值观、涉黄、违法犯罪等安全问题，降低大模型拒答率，支持风险问题的正向引导和纠偏。 |  |

# 约束与限制

*说明在实现时所必须满足的条件和所受的限制，以及相应的原因。如：必须使用或者避免的特定技术、工具、编程语言和数据库、企业策略、政府法规或工业标准**。*

# 需求详细描述

本项目将分多次迭代实现，第一次迭代重点实现基础的运维与监控，dify服务管理，智能应用门户，并集成数据脱敏组件与内容安全检测组件：

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama/secuLlm服务的启停--支持参数传入

3. 模型的展示

4. 监控的管理

5. 安全认证

6. 数据脱敏

7. 数据围栏

8.智能应用门户

5. 1. 产品运行环境

软件环境：

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-E-0001 | 操作系统 | Ubuntu 22.04.4 LTS | 高 |
| PR-E-0002 | JAVA库 | OpenJDK 17 | 高 |
| PR-E-0003 | Nginx | sansec/3.20.2 | 高 |
|  |  | 使用公用组件JCE-5.3.3.22版本；  监控组件使用2.1版本？？ | 高 |
|  | Python | python3.10+ |  |
|  | Go |  |  |

硬件配置：

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-E-0004 | AI一体机整体配置 | CPU： Intel Xeon Gold 6430 32核64线程 \*2  内存：32GB DDR4 RECC \*8  硬盘1：480G SATA \*2  硬盘2：3.84T NVME \*2  GPU：GeForce RTX 4090 24GB \*4  电源：2000W \*2  Ukey ：XT200\*3  密码卡：版本待定？？ 62卡 最新版 | 高 |

* 1. 功能清单

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama服务的启停--支持参数传入

3. 模型的展示

4. 监控的管理

5. 安全认证

6. 数据脱敏

7. 数据围栏

8.智能应用门户

* 1. 功能说明

![](data:image/png;base64...)

5. 3. 监控组件

### 监控组件适配

#### PR-F-1001 监控组件安装适配

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-1001 | **需求名称** | 监控组件安装适配 | **优先级** | 高 |
| **需求描述** | 1、需要在AI一体机上安装监控组件以及所需的基础运行环境；  2、保证监控组件在AI一体机上稳定运行；  3、提供可监控指标的汇总表。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 监控组件安装成功，并可稳定运行 | | | | |
| **其它说明** | 不涉及 | | | | |

#### PR-F-1002 监控组件功能测试

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-1002 | **需求名称** | 监控组件功能测试 | **优先级** | 高 |
| **需求描述** | 1.测试监控组件是否可以正确获取各项指标；  2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表； | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1、输出指标汇总表；  2、监控组件各指标采集正常； | | | | |
| **其它说明** | 不涉及 | | | | |

### 监控组件功能扩展

#### PR-F-1101 AI一体机特殊指标整理

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-1101 | **需求名称** | AI一体机特殊指标整理 | **优先级** | 高 |
| **需求描述** | 收集需要单独采集的指标-显卡的数量、显存、温度、进程的使用情况，还需要考虑国产GPU的兼容问题，将指标汇总至指标汇总表；  gpu列表，每个gpu对象包含：温度、显存、已用显存、 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 输出指标汇总表 | | | | |
| **其它说明** | 不涉及 | | | | |

#### PR-F-1102 AI一体机特殊指标获取

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-1102 | **需求名称** | AI一体机特殊指标获取 | **优先级** | 高 |
| **需求描述** | 根据整理的需要单独处理的指标，研究、评估采集方式，并考虑是否可以集成到监控组件，如果需要集成到监控组件，需要支持通过监控组件获取指标。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | *如果可以集成至监控组件，可以通过监控组件获取对应的指标。* | | | | |
| **其它说明** | 不涉及 | | | | |

* 1. 管理系统相关需求

### secuLlama服务/secullm服务管理

secuLlama/secullm服务服务启动

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2001 | **需求名称** | secuLlama服务启动 | **优先级** | 高 |
| **需求描述** | 1、收集secuLlama启动时支持配置哪些参数，支持通过页面可以实现对secuLlama的服务启动，并支持传参；如果传参为空，则使用默认的配置参数；从当前启动脚本上来看，支持的参数：监听端口、SSL配置(tls，gmtls，none)、证书的配置  2、如果服务已经启动，则禁止重复操作；  3、支持后台修改配置：修改配置后需要删除原来容器，重启启动容器。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 可以通过页面完成secuLlama服务的启动，并通过后台可以看到该进程存在；此外，支持启动时传递配置参数。该接口无需压测。 | | | | |
| **其它说明** | 不涉及 | | | | |

secuLlama服务/secullm服务停止

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2002 | **需求名称** | secuLlama服务停止 | **优先级** | 高 |
| **需求描述** | 1、支持通过页面可以实现对secuLlama/secullm服务的服务停止；  2、如果服务已经停止，则禁止重复操作；  3、停止服务时，需要考虑是否有模型正在运行；弹窗需要提示：停止服务，会停止所有的模型。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 可以通过页面完成secuLlama服务的停止，并通过后台可以看到该进程已停止，该接口无需压测。 | | | | |
| **其它说明** | 不涉及 | | | | |

secuLlama服务/secullm服务状态查看

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2003 | **需求名称** | secuLlama服务状态查看 | **优先级** | 高 |
| **需求描述** | 1、可以通过页面看到secuLlama服务/secullm服务的状态，且与后台查询的状态一致；  2、通过后台停止服务后，可以在页面更新状态，由于不需要数据库存储，每次页面显示都是查询的最新状态，因此不存在延迟性。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 可以通过页面查看secuLlama服务的状态。该接口可压测。 | | | | |
| **其它说明** | 不涉及 | | | | |

secuLlama服务/secullm服务版本查看

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2004 | **需求名称** | secuLlama服务版本查看 | **优先级** | 高 |
| **需求描述** | 1、可以通过页面看到secuLlama服务/secullm服务的版本信息。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 可以通过页面查看secuLlama服务的版本信息。该接口可压测。 | | | | |
| **其它说明** | 不涉及 | | | | |

secuLlama服务/secullm服务接口手册下载

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2005 | **需求名称** | 下载接口手册 | **优先级** | 高 |
| **需求描述** | 1、可以通过页面下载接口手册。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 可以通过页面下载接口手册。 | | | | |
| **其它说明** | 不涉及 | | | | |

### 模型管理

* + - 1. 查看正在运行的模型

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2101 | **需求名称** | 查看正在运行的模型 | **优先级** | 高 |
| **需求描述** | 可以通过页面查看正在运行模型有哪些。  需要提供后台新增模型的功能说明。 | | | | |
| **业务流程** | 1、当secuLlama服务出于运行状态时，可以看到运行的模型；  2、当secuLlama服务处于停止状态时，进入该菜单需要提示服务未运行。 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 根据secuLlama的服务不同状态，显示不同的内容。 | | | | |
| **其它说明** | 不涉及 | | | | |

### apikey管理

* + - 1. 查看apikey

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2301 | **需求名称** | 查看apikey | **优先级** | 高 |
| **需求描述** | 可以通过页面查看apikey列表，默认使用\*\*\*\*表示，可以查看明文的apikey。 | | | | |
| **业务流程** |  | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 默认情况下，apikey以\*\*\*表示 2. 点击查看，可以看到明文的apikey 3. 提供复制apikey 的功能按钮 | | | | |
| **其它说明** | 不涉及 | | | | |

### 监控管理

* + - 1. 监控面板开发

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2201 | **需求名称** | 监控面板开发 | **优先级** | 高 |
| **需求描述** | 结合指标汇总表，开发监控面板，用于显示监控指标 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 能够通过监控面板查看指标情况。 | | | | |
| **其它说明** | 不涉及 | | | | |

* + - 1. AI一体机特殊指标梳理

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2202 | **需求名称** | AI一体机特殊指标梳理 | **优先级** | 高 |
| **需求描述** | 需要结合监控组件的特殊指标梳理需求，考虑是将指标采集的实现集成至监控组件，还是监控管理模块；为了方便后续多台设备的监控管理，以及对接监管平台，建议还是集成到监控组件，便于后续扩展。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 无 | | | | |
| **其它说明** | 不涉及 | | | | |

* + - 1. ~~告警管理--本迭代由于不考虑数据库问题，暂不开发~~

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2203 | **需求名称** | 告警管理 | **优先级** | 低 |
| **需求描述** | 通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告警联系人、告警列表、邮件服务器配置、告警历史等。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 告警管理功能正常。 | | | | |
| **其它说明** | 不涉及 | | | | |

### 安全认证

* + - 1. 基于Ukey的认证

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2301 | **需求名称** | 基于Ukey的认证 | **优先级** | 高 |
| **需求描述** | 考虑不使用数据库，利用内置密码卡的密钥对Ukey下发证书，后续登录时，利用密码卡中的密钥对证书进行验签即可。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 使用正确的Ukey可以成功登录管理系统；使用错误的Ukey无法登录。 | | | | |
| **其它说明** | 不涉及 | | | | |

* + - 1. 基于配置文件的认证

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-2302 | **需求名称** | 基于配置文件的认证 | **优先级** | 高 |
| **需求描述** | 通过配置文件可以登录管理系统，具体展示形式、实现方式待定。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 通过配置文件可以登录管理系统。 | | | | |
| **其它说明** | 不涉及 | | | | |

* 1. 智能应用统一门户相关需求

### **基础网关相关需求**

基础网关作为用户访问智能应用统一门户的前置代理，主要功能如下：

1. 用户身份认证：对所有访问统一门户的用户进行身份认证和权限控制
2. 门户管理代理：门户管理员配置门户时，基础网关负责将请求转发到后台管理服务中，完成对门户的配置
3. 门户访问代理：为每一个智能应用统一门户提供独立的访问地址
4. 应用访问代理：用户访问应用时，基础网关负责将用户请求转发到对应的Dify实例中，完成用户、应用、模型的交互

#### 用户身份和用户类型

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 用户身份和用户类型 | **优先级** | 高 |
| **需求描述** | 1. 用户身份：即用户登录门户后系统为用户分配的token，是鉴权时获取用户信息、用户类型的凭证 2. 用户类型分为四类： 3. 系统管理员：super用户，负责Dify实例管理、门户管理、应用模板管理 4. aduit用户：用于查看审计日志和业务日志 5. 门户管理员：oper用户，一个门户至少有一个门户管理员，负责应用管理、门户用户管理、Dify控制台管理 6. 门户用户：普通用户，可以访问某个门户，使用门户下的应用 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 不同类型的用户可以访问对应的页面 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户管理服务API代理

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6512 | **需求名称** | 门户管理服务API代理 | **优先级** | 关闭 |
| **需求描述** | 1. 通过基础网关，实现门户管理服务API的访问代理：前端调用门户管理服务API时，需要先经过基础网关，基础网关进行请求转发，转发至智能门户系统。   说明：鉴权交由统一web平台 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 登录成功后，访问前端页面时，可以正常访问；未登录时，通过复制url无法访问。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户访问代理和鉴权

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6512 | **需求名称** | 门户访问代理 | **优先级** | 高 |
| **需求描述** | 1. 门户访问代理：当门户用户访问门户时，通过基础网关引导用户跳转到正确的门户地址。 2. 门户访问鉴权：基础网关不进行鉴权，仅转发。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 门户用户能跳转到正确的门户页面 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 应用访问代理和鉴权

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6512 | **需求名称** | 应用访问代理 | **优先级** | 高 |
| **需求描述** | 1. 应用访问代理：当门户用户访问应用时，通过基础网关引导用户跳转到正确的应用地址 2. 应用访问鉴权：基础网关不鉴权，仅转发，鉴权由统一web平台处理。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 门户用户登录系统后，能跳转到正确的应用页面。 2. 门户用户未登录系统，通过复制url的方式无法跳转至应用页面。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 动态更新

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6512 | **需求名称** | 动态更新路由 | **优先级** | 高 |
| **需求描述** | 当访问具体dify实例应用的请求到达基础网关后，基础网关先判断当前的路由配置信息是否包含该dify实例，如果包含，则直接转发；如果不包含，则查询数据库，如果数据库中有，则更新路由，继续转发；如果数据库中也没有，返回404. | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | Dify实例存在时，门户用户访问具体应用，访问成功；  Dify实例不存在时，门户用户访问报错。 | | | | |
| **其它说明** | 不涉及 | | | | |

### **门户管理员管理**

#### 新增门户管理员

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 新增门户管理员 | **优先级** | 关闭 |
| **需求描述** | 权限归属：系统管理员   1. 系统管理员可新增门户管理员账号，新增时必须绑定所属门户 2. 一个门户可以绑定多个门户管理员 3. 门户管理员关联到Dify实例中的管理员账号，具有Dify实例访问权限 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户新增成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 编辑门户管理员

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 编辑门户管理员 | **优先级** | 关闭 |
| **需求描述** | 权限归属：系统管理员   1. 系统管理员可编辑门户管理员基本信息，不可更改所属门户 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户编辑成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 删除门户管理员

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 删除门户管理员 | **优先级** | 关闭 |
| **需求描述** | 权限归属：系统管理员   1. 系统管理员可删除门户管理员 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户删除成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户管理员列表

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户管理员列表 | **优先级** | 关闭 |
| **需求描述** | 权限归属：系统管理员   1. 列表展示：   - 分页展示所有门户的管理员信息，默认按创建时间倒序排列。  - 显示字段：用户名、用户类型、所属门户、最后登录时间。   1. 筛选与搜索：   - 支持按用户类型、所属门户组合筛选。  - 支持模糊搜索用户名   1. 操作列：   - 提供“编辑”、“删除”按钮，按钮状态根据用户权限动态显示 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户管理员登录

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户管理员登录 | **优先级** | 关闭 |
| **需求描述** | 1. 登录流程：   - 支持用户名、密码登录方式。  - 密码错误3次后锁定账号5分钟，需联系系统管理员解锁  2. Token机制：  - 登录成功生成JWT Token，有效期24小时，过期前15分钟可自动续期  - 单用户允许多个活跃会话，以支持多端访问 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户管理员退出

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户管理员退出 | **优先级** | 关闭 |
| **需求描述** | 1. 主动退出：   - 用户点击退出后，服务端销毁Token   1. 被动退出：   - token过期，用户下次鉴权失败后跳转至登录页 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

### **门户用户管理**

#### 新增用户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 新增用户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 系统管理员可新增门户用户，新增时必须绑定所属门户 2. 门户管理员可新增门户用户，新增时将用户自动添加至当前门户管理员 3. 门户用户信息包括：   - 用户名称  - 登录密码  - 所属门户管理员 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户新增成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 编辑用户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 编辑用户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 系统管理员可编辑门户用户，修改所属门户管理员 2. 门户管理员可编辑门户用户，不可更改所属门户管理员 3. 可修改内容项：重置密码 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户密码重置成功，可以使用新密码登录 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 删除用户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 删除用户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 系统管理员可删除门户用户 2. 门户管理员可删除门户用户 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户删除成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户用户列表

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户用户列表 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 列表展示：   - 分页展示所有门户用户，默认按创建时间倒序排列。  - 显示字段：用户名、认证模式、所属门户管理员、状态（启用/禁用）、最后登录时间。   1. 筛选与搜索：   - 支持按所属门户管理源、状态组合筛选。  - 支持模糊搜索用户名   1. 操作列：   - 提供“编辑”、“禁用/启用”、“删除”按钮，按钮状态根据用户权限动态显示 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户列表显示成功，输入搜索条件，可显示符合条件的数据。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户用户登录

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户用户登录 | **优先级** | 高 |
| **需求描述** | 1. 登录流程：   - 支持用户名、密码登录方式。  - 密码错误5次后锁定账号1分钟  - 统一web平台进行用户名、密码验证成功后，智能门户进行用户状态的鉴权，如果用户处于禁用状态，提示联系管理员启用；如果处于启用状态，重定向到该用户所属的门户首页   1. Token机制：   - 登录成功生成JWT Token，有效期半小时  - 单用户允许多个活跃会话，以支持多端访问 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 启用状态用户登录成功；禁用状态用户登录后进行提示。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户用户退出

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户用户退出 | **优先级** | 高 |
| **需求描述** | 1. 主动退出：   - 用户点击退出后，服务端销毁Token   1. 被动退出：   - token过期时，用户下次鉴权失败后跳转至登录页 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户退出系统成功，返回登录页面。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 启用门户用户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 启用门户用户 | **优先级** | 高 |
| **需求描述** | 1. 启用用户：  - 新增用户时，可以设置用户为启用状态，只有状态为“启用”的用户才能访问统一门户  - 支持从用户列表中批量选择用户，设置启用状态 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户启用成功，可以登录。 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 禁用门户用户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 禁用门户用户 | **优先级** | 高 |
| **需求描述** | 1. 禁用用户：   - 新增用户时，可以设置用户为禁用状态，状态为“禁用”的用户无法访问统一门户  - 支持从用户列表中批量选择用户，设置禁用状态  - ~~被禁用的用户，无法登录统一门户，已有Token立即失效~~ | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户禁用成功，未登录的客户端登陆后进行提示；已登录的客户端进行提示。 | | | | |
| **其它说明** | 不涉及 | | | | |

### **门户管理（Dify实例管理）**

#### 创建门户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 创建门户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员   1. 系统管理员创建门户时，需填写：   - 门户名称  - 门户标题、图标、logo、访问地址前缀、主题  - 首页配置：选择默认应用  - 访问控制：匿名访问、授权访问（门户用户绑定该门户后才能访问）  - 安全控制：  --- 是否开启知识库透明加密  --- 门户管理员账号、密码   1. 一个门户对应一个Dify实例，创建门户时后端自动创建门户对应的Dify实例 2. 门户状态：初始化中、初始化失败、运行中、已停止、启动中、异常、删除中、删除失败 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 门户创建完成后，能通过门户访问地址进入门户 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 编辑门户信息

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 编辑门户信息 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 修改门户名称、门户标题、图标、logo、主题 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户编辑成功 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户列表

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户列表 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员   1. 列表功能：   - 分页展示所有门户，显示字段：门户名称、状态（创建中/创建失败/运行中/已停止/异常）、关联实例  - 支持按状态、门户名称筛选   1. 快捷操作：   - 提供“启动/停止”、“删除”、“跳转管理端”入口，状态变更需二次确认。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 门户列表正常加载 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 启动门户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户列表 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 启动流程   - 在门户列表页操作“启动”门户  - 后端自动拉起门户对应的Dify实例  - 后端更新门户状态为已启动、异常  - 后端周期检测Dify实例的健康状态，连续3次检测失败则标记为“异常” | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 启动后门户状态变为运行中，且门户网站能正常访问 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 停止门户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 停止门户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 停止流程   - 在门户列表页操作“停止”门户  - 后端自动停止门户对应的Dify实例，并将门户状态更新为已停止 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 停止后门户状态变为已停止，且门户网站无法访问 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 删除门户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 删除门户 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员   1. 状态为创建中、运行中、删除中的门户不支持删除 2. 删除流程：   - 在门户列表页操作“删除”门户  - 前端给出二次确认  - 后端开始删除门户，并将门户状态改为“删除中”  - 后端删除Dify实例对应的容器  - 后端删除Dify实例下产生的数据：应用、历史会话、知识库、数据库 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 门户删除后管理员无法看到门户信息，门户用户无法访问该门户，服务器上该门户对应的Dify实例数据已删除 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 跳转管理端

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 跳转管理端 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 在门户列表页点击门户右侧“跳转管理端”按钮，浏览器打开Dify管理端页面 2. ~~免密登录：用户进入Dify管理端页面无需登录，平台自动以管理员身份访问Dify管理端页面~~ | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 浏览器能打开Dify管理端页面 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 跳转门户

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 跳转管理端 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 在门户列表页点击门户右侧“跳转门户”按钮，浏览器打开对应门户的首页 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 浏览器能打开对应门户的首页 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 首页配置（迭代2）

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 首页配置 | **优先级** | 低 |
| **需求描述** | 权限归属：系统管理员、门户管理员  用户进入门户首页，默认展示一个问答式应用，用户可直接使用   1. 平台拉取Dify实例中的现有问答式应用列表 2. 选择某个应用保存后，用户访问门户时，首页中使用的应用即默认应用 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 访问控制配置（迭代2）

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 访问控制配置 | **优先级** | 低 |
| **需求描述** | 权限归属：系统管理员、门户管理员   1. 匿名访问：通过门户地址无需登录直接访问 2. 授权访问：访问门户地址后，需要先登录才能访问 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

### **Dify容器化多实例部署改造**

#### Dify多实例部署&网络隔离&目录隔离

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | Dify多实例部署&网络隔离&目录隔离 | **优先级** | 高 |
| **需求描述** | 1. 基于Docker Compose实现Dify容器化部署 2. 实现在一台宿主机上部署多个实例 3. 每个实例使用独立的网络命名空间 4. 每个实例使用独立的磁盘目录 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 在门户管理端支持创建多个门户，在一体机中支持部署多套Dify实例 2. 各Dify实例中，知识库中的文档保存在宿主机中的独立目录中，不交叉存储 | | | | |
| **其它说明** | 不涉及 | | | | |

#### 初始化供应商和模型

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 初始化供应商和模型 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员   1. Dify部署好后，自动完成供应商和模型的初始化 2. 模型信息，通过SecuLlama API拉取 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户创建好统一门后，Dify实例自动部署，Dify中的供应商和模型自动完成初始化，内置应用无需人工配置即可正常使用 | | | | |
| **其它说明** | 不涉及 | | | | |

#### Dify实例自动化部署

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | Dify实例自动化部署 | **优先级** | 高 |
| **需求描述** | 权限归属：系统管理员   1. 用户创建门户后，后台自动完成Dify实例的部署、初始化，无需人工干预 2. 后台将Dify实例的状态实时同步到门户中：初始化中、初始化失败、运行中、已停止、启动中、异常、删除中、删除失败 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 用户创建好统一门后，Dify实例自动部署，无需人工介入 | | | | |
| **其它说明** | 不涉及 | | | | |

### **统一门户**

#### 访问门户首页

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 访问门户首页 | **优先级** | 高 |
| **需求描述** | 权限归属：门户用户、门户管理员、系统管理员   1. 门户用户通过登录页登录后，自动跳转到门户首页 2. 门户管理员、系统管理员可在门户列表页中，点击后方的“跳转门户”，一键跳转到门户首页 3. 在门户的任意页面点击门户logo后，自动跳转到门户首页  ![](data:image/png;base64...) | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 用户信息展示

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户用户信息 | **优先级** | 高 |
| **需求描述** | 权限归属：门户用户、门户管理员、系统管理员   1. 在统一门户任意页面固定位置，展示当前登录的用户信息，例如用户名称 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 门户首页对话

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户首页对话 | **优先级** | 高 |
| **需求描述** | 权限归属：门户用户、门户管理员、系统管理员   1. 用户进入门户首页后，自动为用户展示门户默认的应用，用户可以直接使用该应用进行交互式对话 2. 门户首页应用支持用户查看在当前应用的会话历史列表，可选择某历史会话后继续对话 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 应用探索（门户应用列表）

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 门户应用列表 | **优先级** | 高 |
| **需求描述** | 权限归属：门户用户、门户管理员、系统管理员   1. 用户进入探索页后，页面显示该门户对应Dify实例中的应用（应用探索页只展示已启用的应用） 2. 应用按照标签分组展示，每个应用展示内容有应用名称、logo、应用简介 3. 标签来自Dify管理后台中的预置数据和门户管理员修改的数据 4. 点击应用后跳转到应用对话页 5. 支持用户收藏某应用  ![](data:image/png;base64...) | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 应用对话页

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 应用对话页 | **优先级** | 高 |
| **需求描述** | 权限归属：门户用户、门户管理员、系统管理员   1. 用户点击快捷访问栏中的应用，进入应用对话页 2. 在应用对话页，用户可以直接使用该应用进行交互式对话 3. 应用会话页支持用户查看当前应用的会话历史列表，可选择某历史会话后继续对话  ![](data:image/png;base64...) | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

### 预置应用

#### 通用助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 通用助手 | **优先级** | 高 |
| **需求描述** | 通用智能助手，支持带有记忆的对话、支持上传文档进行问答 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 图片转文字

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 图片转文字 | **优先级** | 高 |
| **需求描述** | 上传图片，识别图中文字 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 英文翻译官

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 英文翻译官 | **优先级** | 高 |
| **需求描述** | 支持中英文互相翻译 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 文章润色

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 文章润色 | **优先级** | 高 |
| **需求描述** | 选择需要的语言风格、字数，对原有文章进行润色 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 图表生成器

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 图表生成器 | **优先级** | 高 |
| **需求描述** | 使用自然语言将数据或表格生成折线图、柱状图、饼图。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 英文全书翻译

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 英文全书翻译 | **优先级** | 高 |
| **需求描述** | 英文全书翻译 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 公文写作

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 公文写作 | **优先级** | 高 |
| **需求描述** | 根据特定主题和内容生成15种法定格式的公文 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 编码助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 编码助手 | **优先级** | 高 |
| **需求描述** | 编程专家，可以实现多种编程语言的代码生成、智能注释、代码纠错、错误解析、多语言转换等功能。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### UI设计助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | UI设计助手 | **优先级** | 高 |
| **需求描述** | 根据自然语言描述生成html代码，可以点击预览 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### latex公式编辑&识别

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | latex公式编辑&识别 | **优先级** | 高 |
| **需求描述** | 支持自然语言描述生成latex格式的公式并进行渲染；支持识别图片中的公式（每次上传一条公式），生成图中公式的latex代码。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### Arxiv论文助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | Arxiv论文助手 | **优先级** | 高 |
| **需求描述** | 使用自然语言查询Arxiv网站上的论文 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### 数据库查询助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | 数据库查询助手 | **优先级** | 高 |
| **需求描述** | 使用自然语言查询数据库，可以查询数据库结构和数据库中表的数据。需要使用URI与数据库进行连接，支持mysql、oracle、oracle11g、postgresql或mssql。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

#### shell指令助手

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6511 | **需求名称** | shell指令助手 | **优先级** | 高 |
| **需求描述** | 根据自然语言生成相应的shell指令，并指定某个指定在目标服务器上执行  敏感指令会被拦截 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** |  | | | | |
| **其它说明** | 不涉及 | | | | |

* 1. 数据围栏系统

为保障企业的数据安全和隐私安全，一体机提供一系列针对敏感数据的识别和处置方案，其中包括敏感数据识别算法，数据脱敏处理方式，业务自定义的配置选项和海量数据处理能力。能够应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理。

随着大语言模型在文本生成、对话交互等场景的广泛应用，其输出内容可能涉及政治、暴力、色情等敏感信息，存在合规风险。为满足法律法规及企业安全要求，需要在一体机内部构建内容审核组件，实时检测并拦截敏感内容，确保输出内容的安全性与合规性。

### 6.7.1 数据脱敏组件

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6701 | **需求名称** | 数据脱敏组件 | **优先级** | 高 |
| **需求描述** | 数据脱敏组件需要提供对敏感数据进行识别和处置的能力。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 不涉及 | | | | |
| **其它说明** | 不涉及 | | | | |

### 6.7.2 内容审核组件

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6702 | **需求名称** | 内容审核组件 | **优先级** | 高 |
| **需求描述** | 内容审核组件需要对涉及政治、暴力、色情等敏感信息的内容进行检测和拦截。 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 不涉及 | | | | |
| **其它说明** | 不涉及 | | | | |

### 6.7.3 组件集成

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6703 | **需求名称** | 数据脱敏与内容审核组件集成 | **优先级** | 高 |
| **需求描述** | 1. 将数据脱敏组件与内容审核组件集成为数据围栏系统； 2. 数据围栏系统以docker形式部署在一体机中； 3. 数据围栏系统以restful api的方式提供服务； 4. 能够测试数据围栏服务是否正常工作； | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 可通过查看正在运行的容器，确认数据围栏系统已部署； 2. 可通过发出restful api请求测试数据围栏系统能否正常工作； | | | | |
| **其它说明** | 不涉及 | | | | |

### 6.7.4 数据围栏系统启用与禁用

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6704 | **需求名称** | 数据围栏系统启用与禁用 | **优先级** | 高 |
| **需求描述** | 1. 预置应用默认启用数据围栏系统； 2. 新建应用可以选择是否启用数据围栏系统； 3. 已安装应用允许对数据围栏的启用和禁用进行修改； | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 1. 预置应用的数据围栏系统开关默认开启； 2. 新建应用时能够选择是否启用数据围栏系统； 3. 能够对已安装应用的数据围栏系统进行启用与禁用； | | | | |
| **其它说明** | 不涉及 | | | | |

* 1. 产品外观需求

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-F-6801 | **需求名称** | 服务器铭牌、外观需求 | **优先级** | 高 |
| **需求描述** | 外购整机需要：  1、铭牌：粘贴铭牌  2、外观要求：粘贴公司相关商标、标签 | | | | |
| **业务流程** |  | | | | |
| **输入输出约束** |  | | | | |
| **验收标准** | 外观满足正常出货要求 | | | | |
| **其它说明** |  | | | | |

* 1. 接口需求

*无。*

* 1. 界面需求

*无*

* 1. 性能需求

*不涉及*

* 1. 可靠性/可用性需求

*产品在规定的条件下，在规定的时间内，在一定条件下无故障的完成规定的功能的能力或可能性称为可靠性。对那些发生质量事故会造成巨大损失或危及人身、社会安全的产品，可靠性是使用过程中主要的质量指标。可靠性可以通过平均无故障时间、平均修复时间、平均失效时间等指标体现。*

*可用性是在某个特定时间，系统能够正常运行的概率或时间占有率期望值。通常使用N个九来表示系统可用性。*

*如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的可靠性/可用性相关要求内容，如完全遵守，可以直接写符合《\*\*\*》标准第\*章节安全性要求。*

*原则上产品需要满足公司发布的《产品安全基线需求》，并参考竞品有竞争力的可用性/可靠性能力，具体内容参照《产品安全基线》。*

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|  | 压测7天 | 压测7天能够稳定运行 | 低 | 压测正常 |

*注：*

1. *需求编号采用PR-R-\*\*\*\*的形式，PR代表产品，R代表可靠性需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*
   1. 安全性需求

*保护产品的要素，以防止各种非法的访问、使用，修改、破坏或者泄密。比如：*

1. *利用可靠的密码技术。*
2. *掌握特定的记录或历史数据集。*
3. *给不同的模块分配不同的功能。*
4. *限定一个程序中某些区域的通信。*
5. *计算临界值的检查和。*

*如果该产品有国家或国际标准、行业标准可循，可以摘录标准中的安全性要求内容，如完全遵守，可以直接写符合《\*\*\*》标准第\*章节安全性要求。*

*原则上产品需要满足公司发布的《产品安全基线需求》，具体内容参照《产品安全基线》。*

*注：*

1. *需求编号采用PR-S-\*\*\*\*的形式，PR代表产品，S代表安全需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*

### 6.13.1 知识库加密

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **需求编号** | PR-S-0001 | **需求名称** | 知识库加密 | **优先级** | 高 |
| **需求描述** | 实现对知识库中所有文档原始文件的透明加密：   1. 用户对知识库文档上传、文档搜索、内容搜索、内容编辑、删除等操作均为无感操作 2. 仅授权用户（包括用户从属进程）才能访问文档原始文件 | | | | |
| **业务流程** | 不涉及 | | | | |
| **输入输出约束** | 不涉及 | | | | |
| **验收标准** | 知识库加密动作不影响用户对知识库的操作  非授权用户无法访问知识库中的原始文档 | | | | |
| **其它说明** | 不涉及 | | | | |

* 1. 可维护性需求

*所有提高可维护性或可扩展性的需求。如使用行业标准，编码标准，开放式结构，可兼容语言，备份机复原，数据交换，命名约定，类库，维护访问，维护工具等。可以从维护者角度考虑，产品应该提供的错误码反馈、日志记录、自我诊断等功能。*

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
|  | 服务端日志 | 在系统发生运行故障，或运算错误等情况，需要有日志记录，能够定位问题。 |  |  |

*注：*

1. *需求编号采用PR-M-\*\*\*\*的形式，PR代表产品，M代表可维护性需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*
   1. 工作状态需求

*工作状态需求是指使用本硬件产品的客户或者是本公司其他部门的同事，能够通过硬件设备上的一些输出状态能够及时的获取硬件设备的工作情况，同时在硬件设备出现异常或作出其他反应时，用户能够及时针对特定情况做出反应。*

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
| PR-WS-0001 |  |  |  |  |
| PR-WS-0002 |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

*注：*

*1．需求编号采用PR-WS-\*\*\*\*的形式，PR代表产品，WS代表工作状态，编号从0001开始依次累加。*

*2．填写表格时需要按照需求的层次自行增加子章节。*

* 1. 结构需求

*硬件设备的设计开发过程涉及到硬件设备外观和结构方面的设计，一般硬件设备的结构尺寸会直接决定其应用场景，因此在需求阶段应该制定好硬件设备的结构方面的需求，可使用行业标准、相关政府标准等结构设计。*

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-ST-0001 | 密码卡扩展槽 | 外购4卡/8卡整机，在2网卡、1RAID卡之外，需要额外的PCIE卡槽安装62型密码卡。 | 高 |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |

*注：*

*1．需求编号采用PR-ST-\*\*\*\*的形式，PR代表产品，ST代表结构需求，编号从0001开始依次累加。*

*2．填写表格时需要按照需求的层次自行增加子章节。*

2. 9. 环保需求

*环保相关的需求，一般涉及到硬件部件或者整机对环境、人体等影响的需求，例如要求硬件材料无铅等。*

无特殊要求，依赖供应商的产品规格。

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-E-0001 |  |  |  |
| PR-E-0002 |  |  |  |

*注：*

1. *需求编号采用PR-E-\*\*\*\*的形式，PR代表产品，E代表认证需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*
   1. 认证需求

*产品在销售到特定的国家、地区或者行业之前，由第三方机构进行的准入检测，验证产品是否能满足国家、行业等相关技术标准。如：国密局产品型号认证、国际FIPS认证、公安部销售许可证申请、中国CCC认证，欧盟的CE认证等。*

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 认证等级 | 接口规范 | 优先级 |
| PR-C-0001 |  |  |  |  |  |
| PR-C-0002 |  |  |  |  |  |

*注：*

1. *需求编号采用PR-C-\*\*\*\*的形式，PR代表产品，C代表认证需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*
   1. 用户文档需求

*用户指南、联机帮助、安装指南、配置文件等。*

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-D-0001 | 用户指南 v1.0.0 |  |  |

注：

1. 需求编号采用PR-D-\*\*\*\*的形式，PR代表产品，D代表文档需求，编号从0001开始依次累加。
2. 填写表格时需要按照需求的层次自行增加子章节。
   1. 客户特殊需求

*指客户提出以上需求以外的特殊需求。*

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 | 验收标准 |
| PR-CS-0001 |  |  |  |  |
| PR-CS-0002 |  |  |  |  |

* 1. 法律法规要求

*描述产品需遵循的相关法律法规。*

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-LR-0001 | 中华人民共和国密码法 | 满足相关要求 | 高 |
| PR-LR-0002 | 计算机信息网络国际联网安全保护管理办法 | 满足相关要求 | 高 |
| PR-LR-0003 | 网络安全技术 生成式人工智能服务安全基本要求（TC260-003） | 满足相关要求 | 高 |

注：以上所列通用法律法规，若本产品不满足或有其他标准，请自行修改或添加。

* 1. 国家及行业标准要求

*描述产品需遵循的国家及行业标准要求。*

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-NIS-0001 | GB-T8567-2006计算机软件文档编制规范 | 满足相关要求 | 高 |
| PR-NIS-0002 | GB-T9386-2008计算机软件测试文档编制规范 | 满足相关要求 | 高 |

注：以上所列行标，若本产品不满足或有其他标准，请自行修改或添加。

* 1. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》)
  2. 其他需求

*其他以上未涉及的需求，如使用寿命、可用性、经济性、易用性、效率、可移植性等。如果有明确需要，可在此处添加，否则可裁剪。*

|  |  |  |  |
| --- | --- | --- | --- |
| 需求编号 | 名称 | 需求描述 | 优先级 |
| PR-O-0001 |  |  |  |
| PR-O-0002 |  |  |  |

*注：*

1. *需求编号采用PR-O-\*\*\*\*的形式，PR代表产品，O代表其他需求，编号从0001开始依次累加。*
2. *填写表格时需要按照需求的层次自行增加子章节。*

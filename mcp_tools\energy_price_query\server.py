#!/usr/bin/env python3
"""
能源局估算单价表查询MCP工具服务器
提供能源设备和工程的估算单价查询功能
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List
from datetime import datetime

# FastMCP相关导入
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.prompts.base import Message

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("energy_price_query")

# 创建FastMCP服务器实例
server = FastMCP("energy_price_query")

# 模拟能源局估算单价数据库（保持原有数据结构）
ENERGY_PRICE_DB = {
    "电力设备": {
        "变压器": {
            "10kV配电变压器": {
                "unit": "台",
                "price_range": "8-15万元/台",
                "standard_price": 120000,
                "specifications": "容量100-630kVA",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年设备估算指导价"
            },
            "35kV变压器": {
                "unit": "台", 
                "price_range": "25-45万元/台",
                "standard_price": 350000,
                "specifications": "容量1000-8000kVA",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年设备估算指导价"
            },
            "110kV变压器": {
                "unit": "台",
                "price_range": "80-150万元/台", 
                "standard_price": 1150000,
                "specifications": "容量16000-63000kVA",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年设备估算指导价"
            }
        },
        "输电线路": {
            "10kV架空线路": {
                "unit": "公里",
                "price_range": "15-25万元/公里",
                "standard_price": 200000,
                "specifications": "单回路，钢筋混凝土杆",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年工程估算指导价"
            },
            "35kV架空线路": {
                "unit": "公里",
                "price_range": "35-55万元/公里",
                "standard_price": 450000,
                "specifications": "单回路，钢管杆",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年工程估算指导价"
            },
            "110kV架空线路": {
                "unit": "公里",
                "price_range": "80-120万元/公里",
                "standard_price": 1000000,
                "specifications": "单回路，铁塔",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年工程估算指导价"
            }
        },
        "配电设备": {
            "开关柜": {
                "unit": "面",
                "price_range": "3-8万元/面",
                "standard_price": 55000,
                "specifications": "10kV金属封闭开关柜",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年设备估算指导价"
            },
            "环网柜": {
                "unit": "台",
                "price_range": "12-20万元/台",
                "standard_price": 160000,
                "specifications": "10kV户外环网柜",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年设备估算指导价"
            }
        }
    },
    "工程费用": {
        "土建工程": {
            "变电站土建": {
                "unit": "平方米",
                "price_range": "2000-3500元/平方米",
                "standard_price": 2750,
                "specifications": "110kV变电站主控楼",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年工程估算指导价"
            }
        },
        "安装工程": {
            "设备安装": {
                "unit": "设备价值百分比",
                "price_range": "8%-15%",
                "standard_price": 0.12,
                "specifications": "电力设备安装费用",
                "update_date": "2024-01-01",
                "price_source": "国家能源局2024年工程估算指导价"
            }
        }
    }
}

@server.tool()
async def query_equipment_price(equipment_type: str, specification: str = "") -> List[TextContent]:
    """
    查询特定能源设备的估算单价
    参数:
        equipment_type: 设备类型，如：变压器、输电线路、开关柜等
        specification: 设备规格，如：10kV、35kV、110kV等
    返回:
        JSON格式的查询结果，包含设备的估算单价
    """
    equipment_type = equipment_type.strip()
    specification = specification.strip()
    
    if not equipment_type:
        return [TextContent(
            type="text",
            text="错误：设备类型不能为空"
        )]
    
    # 搜索匹配的设备
    matching_items = []
    
    for category, subcategories in ENERGY_PRICE_DB.items():
        for subcategory, items in subcategories.items():
            for item_name, item_data in items.items():
                # 检查设备类型匹配
                if (equipment_type.lower() in item_name.lower() or 
                    equipment_type.lower() in subcategory.lower()):
                    
                    # 如果指定了规格，进一步筛选
                    if specification:
                        if (specification.lower() in item_name.lower() or
                            specification.lower() in item_data.get("specifications", "").lower()):
                            matching_items.append({
                                "category": category,
                                "subcategory": subcategory,
                                "item_name": item_name,
                                **item_data
                            })
                    else:
                        matching_items.append({
                            "category": category,
                            "subcategory": subcategory,
                            "item_name": item_name,
                            **item_data
                        })
    
    if not matching_items:
        return [TextContent(
            type="text",
            text=f"未找到匹配的设备价格信息：{equipment_type} {specification}"
        )]
    
    result = {
        "query_equipment": equipment_type,
        "query_specification": specification,
        "matching_count": len(matching_items),
        "matching_items": matching_items,
        "query_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

@server.tool()
async def search_price_by_category(category: str, subcategory: str = "") -> List[TextContent]:
    """
    按类别搜索能源设备价格
    参数:
        category: 设备类别，如：电力设备、工程费用
        subcategory: 子类别，如：变压器、输电线路等（可选）
    返回:
        JSON格式的查询结果，包含设备的估算单价
    """
    category = category.strip()
    subcategory = subcategory.strip()
    
    if not category:
        return [TextContent(
            type="text",
            text="错误：类别不能为空"
        )]
    
    # 查找匹配的类别
    matching_category = None
    for cat_name in ENERGY_PRICE_DB.keys():
        if category.lower() in cat_name.lower() or cat_name.lower() in category.lower():
            matching_category = cat_name
            break
    
    if not matching_category:
        return [TextContent(
            type="text",
            text=f"未找到匹配的类别：{category}"
        )]
    
    category_data = ENERGY_PRICE_DB[matching_category]
    
    # 如果指定了子类别，进一步筛选
    if subcategory:
        matching_subcategory = None
        for subcat_name in category_data.keys():
            if (subcategory.lower() in subcat_name.lower() or 
                subcat_name.lower() in subcategory.lower()):
                matching_subcategory = subcat_name
                break
        
        if not matching_subcategory:
            return [TextContent(
                type="text",
                text=f"在类别 '{matching_category}' 中未找到匹配的子类别：{subcategory}"
            )]
        
        items = {matching_subcategory: category_data[matching_subcategory]}
    else:
        items = category_data
    
    result = {
        "category": matching_category,
        "subcategory": subcategory if subcategory else "全部",
        "total_subcategories": len(items),
        "items": items,
        "query_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

@server.tool()
async def calculate_project_cost(equipment_list: List[Dict[str, Any]]) -> List[TextContent]:
    """
    根据设备清单计算项目总成本估算
    参数:
        equipment_list: 设备清单列表，每个item的结构：
                       "equipment_name": {"type": "string"},
                        "quantity": {"type": "number"},
                        "unit": {"type": "string"}
    返回:
        JSON格式的查询结果，包含项目总成本估算
    """
    equipment_list = equipment_list
    
    if not equipment_list:
        return [TextContent(
            type="text",
            text="错误：设备清单不能为空"
        )]
    
    total_cost = 0
    cost_breakdown = []
    not_found_items = []
    
    for equipment in equipment_list:
        equipment_name = equipment.get("equipment_name", "").strip()
        quantity = equipment.get("quantity", 0)
        
        if not equipment_name or quantity <= 0:
            continue
        
        # 查找设备价格
        found_price = None
        for category, subcategories in ENERGY_PRICE_DB.items():
            for subcategory, items in subcategories.items():
                for item_name, item_data in items.items():
                    if (equipment_name.lower() in item_name.lower() or
                        item_name.lower() in equipment_name.lower()):
                        found_price = item_data
                        break
                if found_price:
                    break
            if found_price:
                break
        
        if found_price:
            unit_price = found_price["standard_price"]
            item_total = unit_price * quantity
            total_cost += item_total
            
            cost_breakdown.append({
                "equipment_name": equipment_name,
                "quantity": quantity,
                "unit": found_price["unit"],
                "unit_price": unit_price,
                "total_price": item_total,
                "price_source": found_price["price_source"]
            })
        else:
            not_found_items.append(equipment_name)
    
    result = {
        "equipment_count": len(equipment_list),
        "calculated_items": len(cost_breakdown),
        "not_found_items": not_found_items,
        "cost_breakdown": cost_breakdown,
        "total_cost": total_cost,
        "total_cost_formatted": f"{total_cost:,.2f}元",
        "calculation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "note": "此为估算价格，实际价格可能因市场变化而有所不同"
    }
    
    return [TextContent(
        type="text",
        text=json.dumps(result, ensure_ascii=False, indent=2)
    )]

def main():
    """启动FastMCP服务器"""
    logger.info("启动能源局估算单价表查询FastMCP服务器...")
        
    server.run(transport='stdio')

if __name__ == "__main__":
    main()
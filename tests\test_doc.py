from docx import Document

def extract_chapters(doc_path):
    doc = Document(doc_path)
    chapters = {}
    current_chapter = None
    for para in doc.paragraphs:
        # 判断段落是否为标题，假设标题样式为 'Heading 1'、'Heading 2' 等
        if para.style.name.startswith('Heading'):
            # 提取标题级别，如 'Heading 1' 的级别为 1
            level = int(para.style.name.split(' ')[1])
            # 根据标题级别构建章节结构
            chapter_title = para.text
            if level == 1:
                current_chapter = chapter_title
                chapters[current_chapter] = {}
            elif level == 2 and current_chapter:
                chapters[current_chapter][chapter_title] = []
            elif level == 3 and current_chapter:
                # 如果有三级标题，可继续嵌套
                pass
        else:
            # 提取章节内容
            if current_chapter and para.text.strip() != '':
                # 将段落内容添加到对应章节
                if 'content' not in chapters[current_chapter]:
                    chapters[current_chapter]['content'] = []
                chapters[current_chapter]['content'].append(para.text)
    return chapters

doc_path = '/mnt/d/github/document-ai/docs/密码+人工智能一体机管理系统项目需求规格说明书V1.0.docx'
chapters = extract_chapters(doc_path)
for chapter, content in chapters.items():
    print(f"章节: {chapter}")
    if isinstance(content, dict):
        for sub_chapter, sub_content in content.items():
            print(f"  Sub-chapter: {sub_chapter}")
            if isinstance(sub_content, list):
                for para in sub_content:
                    print(f"    {para}")
            else:
                print(f"    {sub_content}")
    elif isinstance(content, list):
        for para in content:
            print(f"  {para}")
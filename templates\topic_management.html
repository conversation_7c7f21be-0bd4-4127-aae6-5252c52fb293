<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专题管理 - 可研报告评审助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .topic-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .topic-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .topic-content {
            padding: 15px;
        }
        .file-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">可研报告评审助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">汇总报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/topics">专题管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">报告管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reviews">评审管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>专题管理</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTopicModal">
                <i class="fas fa-plus"></i> 添加专题
            </button>
        </div>

        <!-- 专题列表 -->
        <div id="topicsList">
            <!-- 专题卡片将在这里动态生成 -->
        </div>
    </div>

    <!-- 添加专题模态框 -->
    <div class="modal fade" id="addTopicModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加专题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTopicForm">
                        <div class="mb-3">
                            <label for="topicName" class="form-label">专题名称 *</label>
                            <input type="text" class="form-control" id="topicName" required>
                        </div>
                        <div class="mb-3">
                            <label for="topicDescription" class="form-label">专题描述</label>
                            <textarea class="form-control" id="topicDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="outlineFile" class="form-label">编制大纲文件路径</label>
                            <input type="text" class="form-control" id="outlineFile" placeholder="例如: templates/可行性研究报告编制大纲.docx">
                        </div>
                        <div class="mb-3">
                            <label for="guideFile" class="form-label">审查指南文件路径</label>
                            <input type="text" class="form-control" id="guideFile" placeholder="例如: templates/可行性研究报告审查指南.docx">
                        </div>
                        <div class="mb-3">
                            <label for="criteriaFile" class="form-label">审查细则文件路径</label>
                            <input type="text" class="form-control" id="criteriaFile" placeholder="例如: templates/中央预算投资项目审核表.xlsx">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="startDate" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="startDate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="endDate" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTopic()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑专题模态框 -->
    <div class="modal fade" id="editTopicModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑专题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTopicForm">
                        <input type="hidden" id="editTopicId">
                        <div class="mb-3">
                            <label for="editTopicName" class="form-label">专题名称 *</label>
                            <input type="text" class="form-control" id="editTopicName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTopicDescription" class="form-label">专题描述</label>
                            <textarea class="form-control" id="editTopicDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editOutlineFile" class="form-label">编制大纲文件路径</label>
                            <input type="text" class="form-control" id="editOutlineFile">
                        </div>
                        <div class="mb-3">
                            <label for="editGuideFile" class="form-label">审查指南文件路径</label>
                            <input type="text" class="form-control" id="editGuideFile">
                        </div>
                        <div class="mb-3">
                            <label for="editCriteriaFile" class="form-label">审查细则文件路径</label>
                            <input type="text" class="form-control" id="editCriteriaFile">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStartDate" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="editStartDate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editEndDate" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="editEndDate">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateTopic()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalTitle">内容预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="previewModalContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载内容...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取专题列表
        document.addEventListener('DOMContentLoaded', function() {
            loadTopics();
        });

        // 加载专题列表
        async function loadTopics() {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    displayTopics(result.data);
                } else {
                    alert('加载专题列表失败: ' + result.error);
                }
            } catch (error) {
                alert('加载专题列表失败: ' + error.message);
            }
        }

        // 显示专题列表
        function displayTopics(topics) {
            const container = document.getElementById('topicsList');

            if (topics.length === 0) {
                container.innerHTML = '<div class="text-center text-muted"><p>暂无专题，请点击"添加专题"创建第一个专题。</p></div>';
                return;
            }

            container.innerHTML = topics.map(topic => `
                <div class="topic-card">
                    <div class="topic-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">${topic.name}</h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editTopic('${topic.id}')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteTopic('${topic.id}', '${topic.name}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="topic-content">
                        <p class="mb-2">${topic.description || '无描述'}</p>
                        <div class="file-info">
                            <div><strong>编制大纲:</strong> ${topic.outline_file || '未设置'}
                                ${topic.outline_file ? `<button type="button" class="btn btn-xs btn-outline-info ms-2" onclick="previewOutline('${topic.id}')"><i class="fas fa-eye"></i> 预览</button>` : ''}
                            </div>
                            <div><strong>审查指南:</strong> ${topic.guide_file || '未设置'}
                                ${topic.guide_file ? `<button type="button" class="btn btn-xs btn-outline-info ms-2" onclick="previewGuide('${topic.id}')"><i class="fas fa-eye"></i> 预览</button>` : ''}
                            </div>
                            <div><strong>审查细则:</strong> ${topic.criteria_file || '未设置'}
                                ${topic.criteria_file ? `<button type="button" class="btn btn-xs btn-outline-info ms-2" onclick="previewCriteria('${topic.id}')"><i class="fas fa-eye"></i> 预览</button>` : ''}
                            </div>
                            <div><strong>开始日期:</strong> ${topic.start_date || '未设置'}</div>
                            <div><strong>结束日期:</strong> ${topic.end_date || '未设置'}</div>
                            <div><strong>创建时间:</strong> ${new Date(topic.created_at).toLocaleString()}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 保存专题
        async function saveTopic() {
            const formData = new FormData();
            formData.append('name', document.getElementById('topicName').value);
            formData.append('description', document.getElementById('topicDescription').value);
            formData.append('outline_file', document.getElementById('outlineFile').value);
            formData.append('guide_file', document.getElementById('guideFile').value);
            formData.append('criteria_file', document.getElementById('criteriaFile').value);
            formData.append('start_date', document.getElementById('startDate').value);
            formData.append('end_date', document.getElementById('endDate').value);

            try {
                const response = await fetch('/api/topics', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert('专题创建成功');
                    bootstrap.Modal.getInstance(document.getElementById('addTopicModal')).hide();
                    document.getElementById('addTopicForm').reset();
                    loadTopics();
                } else {
                    alert('创建专题失败: ' + result.error);
                }
            } catch (error) {
                alert('创建专题失败: ' + error.message);
            }
        }

        // 编辑专题
        async function editTopic(topicId) {
            try {
                const response = await fetch('/api/topics');
                const result = await response.json();

                if (result.success) {
                    const topic = result.data.find(t => t.id === topicId);
                    if (topic) {
                        document.getElementById('editTopicId').value = topic.id;
                        document.getElementById('editTopicName').value = topic.name;
                        document.getElementById('editTopicDescription').value = topic.description || '';
                        document.getElementById('editOutlineFile').value = topic.outline_file || '';
                        document.getElementById('editGuideFile').value = topic.guide_file || '';
                        document.getElementById('editCriteriaFile').value = topic.criteria_file || '';
                        document.getElementById('editStartDate').value = topic.start_date || '';
                        document.getElementById('editEndDate').value = topic.end_date || '';

                        new bootstrap.Modal(document.getElementById('editTopicModal')).show();
                    }
                }
            } catch (error) {
                alert('获取专题信息失败: ' + error.message);
            }
        }

        // 更新专题
        async function updateTopic() {
            const topicId = document.getElementById('editTopicId').value;
            const formData = new FormData();
            formData.append('name', document.getElementById('editTopicName').value);
            formData.append('description', document.getElementById('editTopicDescription').value);
            formData.append('outline_file', document.getElementById('editOutlineFile').value);
            formData.append('guide_file', document.getElementById('editGuideFile').value);
            formData.append('criteria_file', document.getElementById('editCriteriaFile').value);
            formData.append('start_date', document.getElementById('editStartDate').value);
            formData.append('end_date', document.getElementById('editEndDate').value);

            try {
                const response = await fetch(`/api/topics/${topicId}`, {
                    method: 'PUT',
                    body: formData
                });
                const result = await response.json();

                if (result.success) {
                    alert('专题更新成功');
                    bootstrap.Modal.getInstance(document.getElementById('editTopicModal')).hide();
                    loadTopics();
                } else {
                    alert('更新专题失败: ' + result.error);
                }
            } catch (error) {
                alert('更新专题失败: ' + error.message);
            }
        }

        // 删除专题
        async function deleteTopic(topicId, topicName) {
            if (!confirm(`确定要删除专题"${topicName}"吗？此操作不可恢复。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/topics/${topicId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.success) {
                    alert('专题删除成功');
                    loadTopics();
                } else {
                    alert('删除专题失败: ' + result.error);
                }
            } catch (error) {
                alert('删除专题失败: ' + error.message);
            }
        }

        // 预览编制大纲
        async function previewOutline(topicId) {
            await showPreview(topicId, 'outline', '编制大纲预览');
        }

        // 预览审查指南
        async function previewGuide(topicId) {
            await showPreview(topicId, 'guide', '审查指南预览');
        }

        // 预览审查细则
        async function previewCriteria(topicId) {
            await showPreview(topicId, 'criteria', '审查细则预览');
        }

        // 显示预览
        async function showPreview(topicId, type, title) {
            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            document.getElementById('previewModalTitle').textContent = title;

            // 重置内容为加载状态
            document.getElementById('previewModalContent').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载内容...</p>
                </div>
            `;

            modal.show();

            try {
                const response = await fetch(`/api/topics/${topicId}/${type}`);
                const result = await response.json();

                if (result.success) {
                    displayPreviewContent(result.data, type);
                } else {
                    document.getElementById('previewModalContent').innerHTML = `
                        <div class="alert alert-danger">
                            <p>加载内容失败: ${result.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('previewModalContent').innerHTML = `
                    <div class="alert alert-danger">
                        <p>加载内容失败: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 显示预览内容
        function displayPreviewContent(data, type) {
            let content = '';

            if (type === 'outline') {
                content = `
                    <div class="mb-3">
                        <p class="text-muted">文件路径: ${data.file_path}</p>
                    </div>
                    <div class="accordion" id="outlineAccordion">
                `;

                Object.entries(data.outline).forEach(([chapterTitle, chapterContent], index) => {
                    const collapseId = `outlineCollapse${index}`;
                    const isFirst = index === 0;

                    content += `
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="outlineHeading${index}">
                                <button class="accordion-button ${isFirst ? '' : 'collapsed'}" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                                        aria-expanded="${isFirst}" aria-controls="${collapseId}">
                                    ${chapterTitle}
                                </button>
                            </h2>
                            <div id="${collapseId}" class="accordion-collapse collapse ${isFirst ? 'show' : ''}"
                                 aria-labelledby="outlineHeading${index}" data-bs-parent="#outlineAccordion">
                                <div class="accordion-body">
                                    <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">${chapterContent || '该章节内容为空'}</pre>
                                </div>
                            </div>
                        </div>
                    `;
                });

                content += '</div>';
            } else if (type === 'guide') {
                content = `
                    <div class="mb-3">
                        <p class="text-muted">文件路径: ${data.file_path}</p>
                    </div>
                    <div class="p-3 bg-light rounded">
                        <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">${data.guide_content}</pre>
                    </div>
                `;
            } else if (type === 'criteria') {
                content = `
                    <div class="mb-3">
                        <p class="text-muted">文件路径: ${data.file_path}</p>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 80px;">编号</th>
                                    <th style="width: 150px;">审查范畴</th>
                                    <th>评审细则</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                data.criteria.forEach(criterion => {
                    content += `
                        <tr>
                            <td><strong>${criterion.id}</strong></td>
                            <td>${criterion.category}</td>
                            <td>${criterion.content}</td>
                        </tr>
                    `;
                });

                content += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            document.getElementById('previewModalContent').innerHTML = content;
        }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
